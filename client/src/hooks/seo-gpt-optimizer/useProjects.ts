/**
 * SEO & GPT Optimizer™ - Projects Hook
 * Custom hook for managing projects
 */

import { useState, useCallback, useEffect } from 'react';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import {
  SEOGPTProject,
  ProjectCreateRequest,
  ProjectFilters,
  SortOptions,
  LoadingState,
  ErrorState
} from '../../types/seo-gpt-optimizer';

interface UseProjectsReturn {
  // State
  projects: SEOGPTProject[];
  currentProject: SEOGPTProject | null;
  loading: LoadingState;
  error: ErrorState;
  totalProjects: number;
  
  // Actions
  loadProjects: (filters?: ProjectFilters, sort?: SortOptions) => Promise<void>;
  createProject: (request: ProjectCreateRequest) => Promise<string | null>;
  loadProject: (projectId: string) => Promise<void>;
  updateProject: (projectId: string, updates: Partial<SEOGPTProject>) => Promise<void>;
  deleteProject: (projectId: string) => Promise<void>;
  searchProjects: (query: string, filters?: ProjectFilters) => Promise<void>;
  clearCurrentProject: () => void;
  clearError: () => void;
  
  // Computed values
  hasProjects: boolean;
  activeProjects: SEOGPTProject[];
  recentProjects: SEOGPTProject[];
}

export const useProjects = (): UseProjectsReturn => {
  const [projects, setProjects] = useState<SEOGPTProject[]>([]);
  const [currentProject, setCurrentProject] = useState<SEOGPTProject | null>(null);
  const [totalProjects, setTotalProjects] = useState(0);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  });
  const [error, setError] = useState<ErrorState>({
    hasError: false,
    message: '',
    code: ''
  });

  const loadProjects = useCallback(async (
    filters?: ProjectFilters,
    sort?: SortOptions,
    limit: number = 20,
    offset: number = 0
  ) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Cargando proyectos...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.getProjects(filters, sort, limit, offset);

      if (response.status === 'success' && response.data) {
        // Normalizar los proyectos con valores por defecto
        const normalizedProjects = (response.data.projects || []).map((project: any) => ({
          ...project,
          current_gpt_rank_score: project.current_gpt_rank_score || 0,
          target_gpt_rank_score: project.target_gpt_rank_score || 95,
          best_gpt_rank_score: project.best_gpt_rank_score || 0,
          content_length: project.content_length || 0,
          word_count: project.word_count || 0,
          seo_score: project.seo_score || 0
        }));

        setProjects(normalizedProjects);
        setTotalProjects(response.data.total || 0);
        setLoading({
          isLoading: false,
          message: 'Proyectos cargados',
          progress: 100
        });
      } else {
        throw new Error(response.error_message || 'Error al cargar proyectos');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'LOAD_PROJECTS_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al cargar proyectos',
        progress: 0
      });
    }
  }, []);

  const createProject = useCallback(async (request: ProjectCreateRequest): Promise<string | null> => {
    try {
      setLoading({
        isLoading: true,
        message: 'Creando proyecto...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.createProject(request);

      if (response.status === 'success' && response.data) {
        const newProjectId = response.data.project_id;
        
        // Reload projects to include the new one
        await loadProjects();
        
        setLoading({
          isLoading: false,
          message: 'Proyecto creado exitosamente',
          progress: 100
        });

        return newProjectId;
      } else {
        throw new Error(response.error_message || 'Error al crear proyecto');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'CREATE_PROJECT_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al crear proyecto',
        progress: 0
      });
      return null;
    }
  }, [loadProjects]);

  const loadProject = useCallback(async (projectId: string) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Cargando proyecto...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.getProject(projectId);

      if (response.status === 'success' && response.data) {
        // Handle nested response structure from backend
        const projectData = (response.data as any).data || response.data;

        // Asegurar que todos los campos necesarios existen con valores por defecto
        const normalizedProject = {
          ...projectData,
          current_gpt_rank_score: projectData.current_gpt_rank_score || 0,
          target_gpt_rank_score: projectData.target_gpt_rank_score || 95,
          best_gpt_rank_score: projectData.best_gpt_rank_score || 0,
          content_length: projectData.content_length || 0,
          word_count: projectData.word_count || 0,
          seo_score: projectData.seo_score || 0
        };

        setCurrentProject(normalizedProject);
        setLoading({
          isLoading: false,
          message: 'Proyecto cargado',
          progress: 100
        });
      } else {
        throw new Error(response.error_message || 'Error al cargar proyecto');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'LOAD_PROJECT_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al cargar proyecto',
        progress: 0
      });
    }
  }, []);

  const updateProject = useCallback(async (
    projectId: string,
    updates: Partial<SEOGPTProject>
  ) => {
    try {
      console.log('🔥 UPDATE PROJECT - ID:', projectId, 'Updates:', updates);
      setLoading({
        isLoading: true,
        message: 'Actualizando proyecto...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.updateProject(projectId, updates);
      console.log('🔥 UPDATE PROJECT - Response:', response);

      if (response.status === 'success' && response.data) {
        // The API service now handles the nested response structure
        const updatedProject = response.data;
        console.log('🔥 UPDATE PROJECT - Updated project:', updatedProject);
        
        // Update current project if it's the one being updated
        if (currentProject?.project_id === projectId) {
          setCurrentProject(updatedProject);
        }

        // Update in projects list
        setProjects(prev => 
          prev.map(project => 
            project.project_id === projectId ? updatedProject : project
          )
        );

        setLoading({
          isLoading: false,
          message: 'Proyecto actualizado',
          progress: 100
        });
      } else {
        console.error('🔥 UPDATE PROJECT - Error response:', response);
        throw new Error(response.error_message || 'Error al actualizar proyecto');
      }
    } catch (err) {
      console.error('🔥 UPDATE PROJECT - Exception:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'UPDATE_PROJECT_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al actualizar proyecto',
        progress: 0
      });
    }
  }, [currentProject]);

  const deleteProject = useCallback(async (projectId: string) => {
    try {
      console.log('🔥 DELETE PROJECT - ID:', projectId);
      setLoading({
        isLoading: true,
        message: 'Eliminando proyecto...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.deleteProject(projectId);
      console.log('🔥 DELETE PROJECT - Response:', response);

      if (response.status === 'success') {
        // Remove from projects list
        setProjects(prev => prev.filter(project => project.project_id !== projectId));
        setTotalProjects(prev => prev - 1);

        // Clear current project if it's the one being deleted
        if (currentProject?.project_id === projectId) {
          setCurrentProject(null);
        }

        setLoading({
          isLoading: false,
          message: 'Proyecto eliminado',
          progress: 100
        });
      } else {
        console.error('🔥 DELETE PROJECT - Error response:', response);
        throw new Error(response.error_message || 'Error al eliminar proyecto');
      }
    } catch (err) {
      console.error('🔥 DELETE PROJECT - Exception:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'DELETE_PROJECT_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error al eliminar proyecto',
        progress: 0
      });
    }
  }, [currentProject]);

  const searchProjects = useCallback(async (
    query: string,
    filters?: ProjectFilters
  ) => {
    try {
      setLoading({
        isLoading: true,
        message: 'Buscando proyectos...',
        progress: 50
      });
      setError({ hasError: false });

      const response = await seoGptAPI.searchProjects(query, filters);

      if (response.status === 'success' && response.data) {
        setProjects(response.data.projects);
        setTotalProjects(response.data.total);
        setLoading({
          isLoading: false,
          message: 'Búsqueda completada',
          progress: 100
        });
      } else {
        throw new Error(response.error_message || 'Error en la búsqueda');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError({
        hasError: true,
        message: errorMessage,
        code: 'SEARCH_PROJECTS_ERROR'
      });
      setLoading({
        isLoading: false,
        message: 'Error en la búsqueda',
        progress: 0
      });
    }
  }, []);

  const clearCurrentProject = useCallback(() => {
    setCurrentProject(null);
  }, []);

  const clearError = useCallback(() => {
    setError({ hasError: false });
  }, []);

  // Load projects on mount
  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  // Computed values
  const hasProjects = projects.length > 0;
  const activeProjects = projects.filter(project => 
    ['researching', 'writing', 'optimizing'].includes(project.status)
  );
  const recentProjects = projects
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    .slice(0, 5);

  return {
    // State
    projects,
    currentProject,
    loading,
    error,
    totalProjects,
    
    // Actions
    loadProjects,
    createProject,
    loadProject,
    updateProject,
    deleteProject,
    searchProjects,
    clearCurrentProject,
    clearError,
    
    // Computed values
    hasProjects,
    activeProjects,
    recentProjects
  };
};
