/**
 * Lexical Editor Styles for Emma Content Builder
 * Professional Google Docs-style editor styling
 */

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Editor Container */
.lexical-editor {
  position: relative;
  font-family: 'Arial', sans-serif;
  font-size: 11pt;
  line-height: 1.5;
  color: #202124;
}

/* Content Editable Area */
.lexical-editor .ContentEditable__root {
  outline: none;
  border: none;
  resize: none;
  cursor: text;
  display: block;
  position: relative;
  tab-size: 1;
  user-select: text;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Placeholder */
.lexical-editor .ContentEditable__placeholder {
  color: #9aa0a6;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 0;
  left: 0;
  font-size: 11pt;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

/* Text Formatting */
.lexical-editor .PlaygroundEditorTheme__bold {
  font-weight: bold;
}

.lexical-editor .PlaygroundEditorTheme__italic {
  font-style: italic;
}

/* Image Handling */
.lexical-editor .PlaygroundEditorTheme__image {
  display: block;
  margin: 12px auto;
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lexical-editor .image-container {
  position: relative;
  display: inline-block;
  margin: 12px 0;
  max-width: 100%;
}

.lexical-editor .image-node {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.lexical-editor .image-node.focused {
  border: 2px solid #3018ef !important;
  box-shadow: 0 0 0 4px rgba(48, 24, 239, 0.1);
}

.lexical-editor .image-node.draggable {
  cursor: move;
}

/* Resize Handles */
.lexical-editor .resize-handle {
  position: absolute;
  background: #3018ef;
  border: 2px solid white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  cursor: nw-resize;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.lexical-editor .resize-handle.corner.se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.lexical-editor .size-indicator {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  pointer-events: none;
}

.lexical-editor .image-caption {
  text-align: center;
  font-style: italic;
  color: #5f6368;
  font-size: 10pt;
  margin-top: 8px;
  padding: 0 16px;
}

.lexical-editor .PlaygroundEditorTheme__underline {
  text-decoration: underline;
}

.lexical-editor .PlaygroundEditorTheme__strikethrough {
  text-decoration: line-through;
}

.lexical-editor .PlaygroundEditorTheme__underlineStrikethrough {
  text-decoration: underline line-through;
}

/* Headings */
.lexical-editor .PlaygroundEditorTheme__h1 {
  font-size: 24px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h2 {
  font-size: 18px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-top: 20px;
  margin-bottom: 6px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h3 {
  font-size: 14px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-top: 16px;
  margin-bottom: 4px;
  padding: 0;
}

/* Paragraphs */
.lexical-editor .PlaygroundEditorTheme__paragraph {
  margin: 0;
  margin-bottom: 8px;
  position: relative;
}

.lexical-editor .PlaygroundEditorTheme__paragraph:last-child {
  margin-bottom: 0;
}

/* Lists */
.lexical-editor .PlaygroundEditorTheme__ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
  list-style-position: inside;
}

.lexical-editor .PlaygroundEditorTheme__ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
  list-style-position: inside;
}

.lexical-editor .PlaygroundEditorTheme__listItem {
  margin: 8px 32px 8px 32px;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked,
.lexical-editor .PlaygroundEditorTheme__listItemUnchecked {
  position: relative;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 24px;
  padding-right: 24px;
  list-style-type: none;
  outline: none;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked {
  text-decoration: line-through;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
  content: '';
  width: 16px;
  height: 16px;
  top: 2px;
  left: 0;
  cursor: pointer;
  display: block;
  background-size: cover;
  position: absolute;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked[dir='rtl']:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked[dir='rtl']:before {
  left: auto;
  right: 0;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before {
  box-shadow: 0 0 0 2px #a6cdfe;
  border-radius: 2px;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before {
  border: 1px solid #999;
  border-radius: 2px;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
  border: 1px solid rgb(61, 135, 245);
  border-radius: 2px;
  background-color: #3d87f5;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9 1L4 6L1 3' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
}

/* Links */
.lexical-editor .PlaygroundEditorTheme__link {
  color: #3d87f5;
  text-decoration: none;
}

.lexical-editor .PlaygroundEditorTheme__link:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* Text Alignment */
.lexical-editor .PlaygroundEditorTheme__textLeft {
  text-align: left;
}

.lexical-editor .PlaygroundEditorTheme__textCenter {
  text-align: center;
}

.lexical-editor .PlaygroundEditorTheme__textRight {
  text-align: right;
}

.lexical-editor .PlaygroundEditorTheme__textJustify {
  text-align: justify;
}

/* Code */
.lexical-editor .PlaygroundEditorTheme__code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.lexical-editor .PlaygroundEditorTheme__codeBlock {
  position: relative;
  margin: 8px 0;
  background-color: rgb(240, 242, 245);
  padding: 8px 8px 8px 52px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 13px;
  line-height: 1.53;
  tab-size: 2;
}

/* Selection */
.lexical-editor .PlaygroundEditorTheme__mark {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
  padding-bottom: 2px;
}

.lexical-editor .PlaygroundEditorTheme__markOverlap {
  background: rgba(255, 212, 0, 0.3);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}

/* Focus */
.lexical-editor:focus-within {
  outline: none;
}

/* Emma Branding */
.lexical-editor .emma-highlight {
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.1) 0%, rgba(221, 58, 90, 0.1) 100%);
  border-radius: 3px;
  padding: 2px 4px;
}

.lexical-editor .emma-suggestion {
  border-bottom: 2px dotted #3018ef;
  cursor: pointer;
}

.lexical-editor .emma-suggestion:hover {
  background: rgba(48, 24, 239, 0.05);
}

/* Advanced Formatting Styles */
.lexical-editor .PlaygroundEditorTheme__subscript {
  font-size: 0.8em;
  vertical-align: sub;
}

.lexical-editor .PlaygroundEditorTheme__superscript {
  font-size: 0.8em;
  vertical-align: super;
}

.lexical-editor .PlaygroundEditorTheme__textCode {
  background-color: rgba(240, 242, 245, 0.8);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', Consolas, Monaco, monospace;
  font-size: 0.9em;
  color: #d73a49;
}

.lexical-editor .PlaygroundEditorTheme__h4 {
  font-size: 16px;
  color: #202124;
  font-weight: 500;
  margin: 0;
  margin-top: 14px;
  margin-bottom: 4px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h5 {
  font-size: 14px;
  color: #202124;
  font-weight: 500;
  margin: 0;
  margin-top: 12px;
  margin-bottom: 3px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h6 {
  font-size: 12px;
  color: #202124;
  font-weight: 500;
  margin: 0;
  margin-top: 10px;
  margin-bottom: 2px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__quote {
  margin: 16px 0;
  padding: 16px;
  border-left: 4px solid #3018ef;
  background: rgba(48, 24, 239, 0.05);
  font-style: italic;
  color: #5f6368;
}

.lexical-editor .PlaygroundEditorTheme__hashtag {
  color: #1a73e8;
  font-weight: 500;
}

/* Custom Text Colors and Backgrounds */
.lexical-editor [style*="color:"] {
  /* Preserve inline color styles */
}

.lexical-editor [style*="background-color:"] {
  /* Preserve inline background color styles */
  padding: 2px 4px;
  border-radius: 2px;
}

.lexical-editor [style*="font-family:"] {
  /* Preserve inline font family styles */
}

.lexical-editor [style*="font-size:"] {
  /* Preserve inline font size styles */
}

/* Enhanced Selection */
.lexical-editor ::selection {
  background: rgba(48, 24, 239, 0.2);
  color: inherit;
}

.lexical-editor ::-moz-selection {
  background: rgba(48, 24, 239, 0.2);
  color: inherit;
}

/* Improved Focus States */
.lexical-editor:focus-within .ContentEditable__root {
  outline: 2px solid rgba(48, 24, 239, 0.3);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Better Typography */
.lexical-editor .PlaygroundEditorTheme__paragraph {
  line-height: 1.6;
  margin-bottom: 12px;
}

.lexical-editor .PlaygroundEditorTheme__paragraph:empty::before {
  content: '';
  display: inline-block;
  height: 1em;
}

/* Improved List Styling */
.lexical-editor .PlaygroundEditorTheme__ul,
.lexical-editor .PlaygroundEditorTheme__ol {
  margin: 8px 0;
  padding-left: 24px;
}

.lexical-editor .PlaygroundEditorTheme__listItem {
  margin: 4px 0;
  line-height: 1.5;
}

/* Fallback list styling - ensure lists are visible even without theme classes */
.lexical-editor ul {
  list-style-type: disc !important;
  margin: 8px 0 !important;
  padding-left: 24px !important;
}

.lexical-editor ol {
  list-style-type: decimal !important;
  margin: 8px 0 !important;
  padding-left: 24px !important;
}

.lexical-editor li {
  margin: 4px 0 !important;
  line-height: 1.5 !important;
  display: list-item !important;
}

/* Enhanced Link Styling */
.lexical-editor .PlaygroundEditorTheme__link {
  color: #3018ef;
  text-decoration: none;
  border-bottom: 1px solid rgba(48, 24, 239, 0.3);
  transition: all 0.2s ease;
}

.lexical-editor .PlaygroundEditorTheme__link:hover {
  color: #dd3a5a;
  border-bottom-color: rgba(221, 58, 90, 0.5);
  background: rgba(48, 24, 239, 0.05);
}

/* Print Styles */
@media print {
  .lexical-editor {
    background: white !important;
    box-shadow: none !important;
    border: none !important;
  }

  .lexical-editor .PlaygroundEditorTheme__paragraph {
    margin-bottom: 8px;
  }

  .lexical-editor .PlaygroundEditorTheme__link {
    color: black !important;
    text-decoration: underline !important;
    border-bottom: none !important;
  }
}
