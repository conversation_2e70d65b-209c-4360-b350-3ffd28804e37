/**
 * SEO & GPT Optimizer™ - Main Dashboard Page
 * Main dashboard for the SEO & GPT Optimizer tool
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  PenTool,
  BarChart3,
  Plus,
  TrendingUp,
  FileText,
  Clock,
  Target,
  Zap,
  Brain,
  BookOpen,
  Archive,
  Settings,
  MoreVertical,
  Edit,
  Trash2,
  Copy,
  ExternalLink
} from 'lucide-react';
import { useLocation } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { useSavedResearch } from '../../hooks/seo-gpt-optimizer/useSavedResearch';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';
import { SEOGPTProject, ProjectCreateRequest, ProjectStatus } from '../../types/seo-gpt-optimizer';
import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import LoadingSpinner from '../../components/seo-gpt-optimizer/shared/LoadingSpinner';
import GPTRankMeter from '../../components/seo-gpt-optimizer/shared/GPTRankMeter';
import ProjectForm from '../../components/seo-gpt-optimizer/project-management/ProjectForm';

interface DashboardStats {
  total_projects: number;
  active_projects: number;
  avg_gpt_rank_score: number;
  total_research_conducted: number;
  improvement_this_month: number;
}

const SEOGPTOptimizerDashboard: React.FC = () => {
  const [, setLocation] = useLocation();
  const {
    projects,
    loading,
    recentProjects,
    activeProjects,
    updateProject,
    deleteProject: deleteProjectHook,
    createProject,
    loadProjects
  } = useProjects();
  const { savedResearch, getRecentResearch } = useSavedResearch();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  // Project form state
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [editingProject, setEditingProject] = useState<SEOGPTProject | null>(null);

  // Load dashboard stats
  useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await seoGptAPI.getDashboardStats();
        if (response.status === 'success' && response.data) {
          setDashboardStats(response.data);
        }
      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      } finally {
        setStatsLoading(false);
      }
    };

    loadStats();
  }, []);

  const handleGoToProjects = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects');
  };

  const handleStartResearch = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/research');
  };

  const handleOpenContentBuilder = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/content-builder');
  };

  const handleViewAnalytics = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/analytics');
  };

  const handleViewSavedResearch = () => {
    setLocation('/dashboard/herramientas/seo-gpt-optimizer/saved-research');
  };

  // Project management functions
  const handleEditProject = (project: SEOGPTProject) => {
    setEditingProject(project);
    setShowProjectForm(true);
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      await deleteProjectHook(projectId);
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  };

  const handleDuplicateProject = async (project: SEOGPTProject) => {
    try {
      const duplicateData: ProjectCreateRequest = {
        title: `${project.title} (Copia)`,
        topic: project.topic,
        target_language: project.target_language,
        content_type: project.content_type,
        target_gpt_rank_score: project.target_gpt_rank_score
      };

      const newProjectId = await createProject(duplicateData);
      if (newProjectId) {
        // Optionally navigate to the new project
        setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${newProjectId}`);
      }
    } catch (error) {
      console.error('Error duplicating project:', error);
    }
  };

  const handleCloseForm = () => {
    setShowProjectForm(false);
    setEditingProject(null);
  };

  const handleSaveProject = async (data: ProjectCreateRequest | Partial<SEOGPTProject>) => {
    try {
      if (editingProject) {
        // Update existing project
        await updateProject(editingProject.project_id, data as Partial<SEOGPTProject>);
        // Reload projects to ensure data consistency
        await loadProjects();
      } else {
        // Create new project
        const projectId = await createProject(data as ProjectCreateRequest);
        if (projectId) {
          setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${projectId}`);
          return;
        }
      }
      setShowProjectForm(false);
      setEditingProject(null);
    } catch (error) {
      console.error('Error saving project:', error);
    }
  };

  // Removed loading screen - show dashboard immediately

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  SEO & GPT Optimizer™
                </h1>
                <p className="text-gray-600 mt-1">
                  Optimiza tu contenido para motores de búsqueda y modelos de IA
                </p>
              </div>
              
              <motion.button
                onClick={handleGoToProjects}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center gap-2 shadow-lg"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Plus className="w-5 h-5" />
                Gestionar Proyectos
              </motion.button>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statsLoading ? (
              Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))
            ) : (
              <>
                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Proyectos Totales</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.total_projects || 0}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <FileText className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Proyectos Activos</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.active_projects || 0}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <Clock className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Puntuación Promedio</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {dashboardStats?.avg_gpt_rank_score?.toFixed(1) || '0.0'}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <Target className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600 text-sm font-medium">Mejora Este Mes</p>
                      <p className="text-2xl font-bold text-green-600">
                        +{dashboardStats?.improvement_this_month?.toFixed(1) || '0.0'}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </motion.div>
              </>
            )}
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleStartResearch}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                <Search className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Research Engine</h3>
              <p className="text-gray-600 mb-4">
                Investiga temas, analiza competidores y encuentra oportunidades de contenido
              </p>
              <div className="flex items-center text-blue-600 font-medium">
                <span>Iniciar Investigación</span>
                <Zap className="w-4 h-4 ml-2" />
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleOpenContentBuilder}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                <PenTool className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Content Builder</h3>
              <p className="text-gray-600 mb-4">
                Crea y optimiza contenido con análisis GPT Rank en tiempo real
              </p>
              <div className="flex items-center text-purple-600 font-medium">
                <span>Crear Contenido</span>
                <Brain className="w-4 h-4 ml-2" />
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleViewAnalytics}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Analytics</h3>
              <p className="text-gray-600 mb-4">
                Analiza el rendimiento y evolución de tus proyectos
              </p>
              <div className="flex items-center text-green-600 font-medium">
                <span>Ver Analytics</span>
                <TrendingUp className="w-4 h-4 ml-2" />
              </div>
            </motion.div>

            <motion.div
              className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={handleViewSavedResearch}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
                <Archive className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Investigaciones</h3>
              <p className="text-gray-600 mb-4">
                Revisa y gestiona tus investigaciones guardadas ({savedResearch.length})
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center text-orange-600 font-medium">
                  <span>Ver Guardadas</span>
                  <BookOpen className="w-4 h-4 ml-2" />
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setLocation('/dashboard/herramientas/seo-gpt-optimizer/research-management');
                  }}
                  className="text-xs text-gray-500 hover:text-orange-600 font-medium flex items-center gap-1"
                >
                  <Settings className="w-3 h-3" />
                  Gestionar
                </button>
              </div>
            </motion.div>
          </div>

          {/* Recent Saved Research */}
          {savedResearch.length > 0 && (
            <motion.div
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">Investigaciones Recientes</h2>
                    <p className="text-gray-600 mt-1">Tus últimas investigaciones guardadas</p>
                  </div>
                  <button
                    onClick={handleViewSavedResearch}
                    className="text-orange-600 hover:text-orange-700 font-medium text-sm flex items-center gap-1"
                  >
                    Ver todas
                    <BookOpen className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="divide-y divide-gray-100">
                {savedResearch.slice(0, 3).map((research, index) => (
                  <motion.div
                    key={research.id}
                    className="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                    onClick={handleViewSavedResearch}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{research.topic}</h3>
                        <div className="flex items-center gap-4 mt-2">
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-700">
                            Confianza: {(research.confidence * 100).toFixed(1)}%
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(research.savedAt).toLocaleDateString('es-ES')}
                          </span>
                        </div>
                      </div>

                      <div className="ml-6">
                        <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                          <Search className="w-6 h-6 text-orange-600" />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Recent Projects */}
          {recentProjects.length > 0 && (
            <motion.div
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">Proyectos Recientes</h2>
                    <p className="text-gray-600 mt-1">Tus últimos proyectos de optimización</p>
                  </div>
                  <button
                    onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer/projects')}
                    className="px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-medium text-sm rounded-lg transition-colors duration-200 flex items-center gap-2"
                  >
                    Ver todos los proyectos
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
              
              <div className="divide-y divide-gray-100">
                {recentProjects.slice(0, 5).map((project, index) => (
                  <ProjectRowWithMenu
                    key={project.project_id}
                    project={project}
                    index={index}
                    onOpen={() => setLocation(`/dashboard/herramientas/seo-gpt-optimizer/project/${project.project_id}`)}
                    onEdit={() => handleEditProject(project)}
                    onDelete={() => handleDeleteProject(project.project_id)}
                    onDuplicate={() => handleDuplicateProject(project)}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Project Form Modal */}
      <ProjectForm
        project={editingProject || undefined}
        isOpen={showProjectForm}
        onClose={handleCloseForm}
        onSave={handleSaveProject}
        loading={false}
      />
    </ErrorBoundary>
  );
};

// Componente para mostrar proyectos con menú de opciones
interface ProjectRowWithMenuProps {
  project: SEOGPTProject;
  index: number;
  onOpen: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

const ProjectRowWithMenu: React.FC<ProjectRowWithMenuProps> = ({
  project,
  index,
  onOpen,
  onEdit,
  onDelete,
  onDuplicate
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  const handleMenuAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);

    switch (action) {
      case 'edit':
        onEdit();
        break;
      case 'delete':
        if (window.confirm('¿Estás seguro de que quieres eliminar este proyecto?')) {
          onDelete();
        }
        break;
      case 'duplicate':
        onDuplicate();
        break;
      case 'open':
        onOpen();
        break;
    }
  };

  return (
    <motion.div
      className="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200 relative"
      onClick={onOpen}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 1.3 + index * 0.1 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{project.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{project.topic}</p>
          <div className="flex items-center gap-4 mt-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              project.status === 'completed' ? 'bg-green-100 text-green-700' :
              project.status === 'writing' ? 'bg-blue-100 text-blue-700' :
              project.status === 'researching' ? 'bg-yellow-100 text-yellow-700' :
              'bg-gray-100 text-gray-700'
            }`}>
              {project.status}
            </span>
            <span className="text-xs text-gray-500">
              {new Date(project.updated_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <GPTRankMeter
            score={project.current_gpt_rank_score}
            grade={project.current_gpt_rank_score >= 90 ? 'A+' :
                   project.current_gpt_rank_score >= 80 ? 'A' :
                   project.current_gpt_rank_score >= 70 ? 'B' : 'C'}
            size="sm"
            showDetails={false}
            animated={false}
          />

          {/* Actions Menu */}
          <div className="relative" ref={menuRef}>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
              className={`p-2 rounded-lg transition-colors duration-200 border ${
                showMenu
                  ? 'text-blue-600 bg-blue-50 border-blue-200'
                  : 'text-gray-600 bg-white border-gray-200 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 shadow-sm'
              }`}
              title="Opciones del proyecto"
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {showMenu && (
              <div
                className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50"
                style={{ zIndex: 9999 }}
              >
                <button
                  key="open"
                  onClick={(e) => handleMenuAction('open', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  Abrir proyecto
                </button>
                <button
                  key="edit"
                  onClick={(e) => handleMenuAction('edit', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  Editar
                </button>
                <button
                  key="duplicate"
                  onClick={(e) => handleMenuAction('duplicate', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Duplicar
                </button>
                <hr key="separator" className="my-2" />
                <button
                  key="delete"
                  onClick={(e) => handleMenuAction('delete', e)}
                  className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Eliminar
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SEOGPTOptimizerDashboard;
