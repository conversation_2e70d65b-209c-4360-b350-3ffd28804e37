/**
 * SEO & GPT Optimizer™ - Content Builder Page
 * Main page for the content builder with real-time analysis
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Save } from 'lucide-react';
import { useLocation, useRoute } from 'wouter';
import toast from 'react-hot-toast';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';
import { seoGptAPI } from '../../services/seo-gpt-optimizer/api';

import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import { ContentBuilder } from '../../components/seo-gpt-optimizer/content-builder';

const ContentBuilderPage: React.FC = () => {
  const [location, setLocation] = useLocation();

  // Extract projectId and contentId from URL using wouter - exactly as you specified
  const [matchNewBlog, newBlogParams] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/projects/:projectId/new-blog');
  const [matchEdit, editParams] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/projects/:projectId/edit/:contentId');

  const projectId = newBlogParams?.projectId || editParams?.projectId;
  const contentId = editParams?.contentId;

  console.log('🔥 ROUTE MATCH - New Blog:', matchNewBlog, 'Edit:', matchEdit);
  console.log('🔥 EXTRACTED - Project ID:', projectId, 'Content ID:', contentId);
  console.log('🔥 NEW BLOG PARAMS:', newBlogParams);
  console.log('🔥 EDIT PARAMS:', editParams);
  
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isCreatingContent, setIsCreatingContent] = useState(false);

  // Determine if this is create or edit mode
  const isEditMode = !!contentId;
  const isCreateMode = !contentId;

  // Load existing content when in edit mode
  useEffect(() => {
    if (isEditMode && projectId && contentId) {
      console.log('🔄 EDIT MODE - Loading existing content:', contentId);
      setIsLoading(true);

      fetch(`/api/v1/seo-gpt/projects/${projectId}/contents/${contentId}`)
        .then(res => res.json())
        .then(data => {
          console.log('🔄 EDIT MODE - Loaded content:', data);
          if (data.status === 'success' && data.data) {
            setTopic(data.data.title || '');
            setContent(data.data.content_html || data.data.content_text || '');
            setHasUnsavedChanges(false);
          }
        })
        .catch(error => {
          console.error('❌ EDIT MODE - Error loading content:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [projectId, contentId, isEditMode]);

  const {
    currentProject,
    loadProject,
    updateProject,
    loading: projectLoading
  } = useProjects();

  // Load project if projectId is provided
  useEffect(() => {
    if (projectId) {
      loadProject(projectId);
    }
  }, [projectId, loadProject]);

  // Load existing content if contentId is provided
  useEffect(() => {
    const loadExistingContent = async () => {
      if (projectId && contentId) {
        try {
          setIsLoading(true);
          console.log(`📖 Loading existing content: ${contentId} from project: ${projectId}`);

          const response = await seoGptAPI.getProjectContent(projectId, contentId);

          if (response.status === 'success' && response.data) {
            setContent(response.data.content_text || '');
            setTopic(response.data.title || '');
            setCurrentContentId(contentId);
            setHasUnsavedChanges(false);
            console.log('✅ Content loaded successfully');
          } else {
            console.error('❌ Failed to load content:', response.message);
            toast.error('Error al cargar el contenido');
          }
        } catch (error) {
          console.error('❌ Error loading content:', error);
          toast.error('Error al cargar el contenido');
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadExistingContent();
  }, [projectId, contentId]);

  // Set initial content from project
  useEffect(() => {
    if (currentProject) {
      setContent(currentProject.content_text || '');
      setTopic(currentProject.topic || '');
    }
  }, [currentProject]);

  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?'
      );
      if (!confirmLeave) return;
    }

    // Navigate back to the project dashboard using clean URL
    setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}`);
  };

  const handleSave = async (contentToSave: string) => {
    // DEBUG LOGS as requested
    console.log("🔥 SAVE DEBUG - projectId:", projectId);
    console.log("🔥 SAVE DEBUG - saving payload:", contentToSave);
    console.log("🔥 SAVE DEBUG - topic:", topic);

    if (!projectId) {
      console.error("❌ SAVE FAILED - No projectId");
      toast.error('Error: No se pudo identificar el proyecto');
      return;
    }

    if (isSaving || isCreatingContent) return;
    setIsSaving(true);

    try {
      if (isEditMode && contentId) {
        // Update existing content
        console.log("🔥 EDIT MODE - Updating content:", contentId);

        const response = await seoGptAPI.updateProjectContent(projectId, contentId, {
          title: topic || 'Documento sin título',
          content_text: contentToSave,
          content_html: contentToSave,
        });

        console.log("🔥 EDIT DEBUG - API Response:", response);

        if (response.status === 'success') {
          setHasUnsavedChanges(false);
          toast.success('Contenido actualizado correctamente');
        } else {
          throw new Error(response.message || 'Error al actualizar');
        }
      } else {
        // Create new content only if we don't already have a contentId
        // This prevents auto-save from creating multiple content items
        if (contentId) {
          console.log("⚠️ Content ID already exists, switching to edit mode");
          setIsEditMode(true);
          // Recursively call with edit mode
          setIsSaving(false);
          return handleSave(contentToSave);
        }

        console.log("🔥 CREATE MODE - Making API call to:", `/api/v1/seo-gpt/projects/${projectId}/contents`);

        setIsCreatingContent(true);
        const response = await seoGptAPI.createProjectContent(projectId, {
          title: topic || 'Documento sin título',
          content_type: 'blog',
          content_text: contentToSave,
          content_html: contentToSave,
        });
        setIsCreatingContent(false);

        console.log("🔥 CREATE DEBUG - API Response:", response);

        if (response.status === 'success' && response.data) {
          setHasUnsavedChanges(false);
          const newContentId = response.data.content_id;
          console.log("✅ CREATE SUCCESS - Content ID:", newContentId);

          // Switch to edit mode to prevent creating multiple content items
          setContentId(newContentId);
          setIsEditMode(true);

          // Update URL to reflect the new content ID
          setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}/content-builder?contentId=${newContentId}`);

          toast.success('Contenido creado y guardado correctamente', {
            action: {
              label: 'Ver en proyecto',
              onClick: () => setLocation(`/dashboard/herramientas/seo-gpt-optimizer/projects/${projectId}`)
            }
          });
        } else {
          console.error("❌ CREATE FAILED - Response:", response);
          throw new Error(response.message || 'Error al crear');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      toast.error(`Error al guardar: ${errorMessage}`);
    } finally {
      setIsSaving(false);
      setIsCreatingContent(false);
    }
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${topic || 'content'}-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  // Sin keyboard shortcuts molestos

  if (!projectId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Proyecto no encontrado
          </h2>
          <p className="text-gray-600 mb-4">
            No se pudo identificar el proyecto.
          </p>
          <button
            onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Volver al Dashboard
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Cargando contenido...
          </h2>
          <p className="text-gray-600">
            Por favor espera mientras cargamos tu contenido.
          </p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    📁 {currentProject ? currentProject.title : 'Content Builder'}
                  </h1>
                  <p className="text-gray-600 text-sm">
                    {currentProject ? currentProject.topic : 'Crea contenido optimizado con análisis en tiempo real'}
                  </p>
                </div>
                {hasUnsavedChanges && (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium">
                    Cambios sin guardar
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Save Button */}
                <button
                  onClick={() => handleSave(content)}
                  disabled={!hasUnsavedChanges || isSaving}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isSaving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="text-sm">Guardando...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span className="text-sm">
                        {isEditMode ? 'Guardar Cambios' : 'Crear Blog'}
                      </span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Editor Simple */}
        <div className="h-[calc(100vh-80px)]">
          <ContentBuilder
            projectId={projectId || 'demo'}
            initialContent={content}
            initialTopic={topic}
            onContentChange={(newContent) => {
              setContent(newContent);
              setHasUnsavedChanges(true);
            }}
            onSave={async (contentToSave, titleToSave) => {
              setTopic(titleToSave);
              await handleSave(contentToSave);
            }}
            onTitleChange={(newTitle) => {
              setTopic(newTitle);
              setHasUnsavedChanges(true);
            }}
            className="h-full"
          />
        </div>


      </div>
    </ErrorBoundary>
  );
};

export default ContentBuilderPage;
