import { useState } from "react";
import {
  FileText,
  Mail,
  MessageSquare,
  Megaphone,
  Target,
  ChevronRight,
  Search,
  X,
  ArrowRight,
  CheckCircle,
  Sparkles,
  ChevronLeft,
  Loader2,
  Code,
  Check,
} from "lucide-react";

import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

// Importamos nuestras estructuras de datos
import {
  contentCategories,
  marketingFunctions,
  funnelStages,
  renderIcon,
  getIconForCategory,
  ContentCategory,
  ContentSubcategory,
} from "@/data/simplified-categories";
import {
  getQuestionsForSubcategory,
  TemplateQuestion,
} from "@/data/category-questions";

// Herramientas destacadas
const featuredTools = [
  {
    id: "blog-writer",
    name: "Blog Writer Pro",
    iconName: "FileText",
    description: "Create SEO-optimized blog posts that drive traffic",
    category: "blog",
    subcategory: "blog-post",
    bgColor: "bg-blue-50",
  },
  {
    id: "social-media",
    name: "Social Media Genius",
    iconName: "MessageSquare",
    description: "Generate platform-specific posts that engage your audience",
    category: "social",
    subcategory: "social-post",
    bgColor: "bg-pink-50",
  },
  {
    id: "email-wizard",
    name: "Email Campaign Wizard",
    iconName: "Mail",
    description: "Craft email sequences that convert prospects to customers",
    category: "email",
    subcategory: "newsletter",
    bgColor: "bg-purple-50",
  },
  {
    id: "ad-copy",
    name: "Ad Copy Creator",
    iconName: "Megaphone",
    description: "Write high-converting ad copy for multiple platforms",
    category: "ads",
    subcategory: "ad-copy",
    bgColor: "bg-green-50",
  },
];

// Categorías para navegar
const browseCategories = [
  {
    id: "content-types",
    name: "Content Types",
    description: "Choose the type of marketing content you need to create",
    iconName: "FileText",
    iconClass: "h-6 w-6 text-blue-500",
    items: contentCategories,
  },
  {
    id: "marketing-functions",
    name: "Marketing Functions",
    description: "Select based on your marketing strategy focus",
    iconName: "Target",
    iconClass: "h-6 w-6 text-purple-500",
    items: marketingFunctions,
  },
  {
    id: "funnel-stage",
    name: "Funnel Stage",
    description: "Choose content based on your customer journey stage",
    iconName: "MessageSquare",
    iconClass: "h-6 w-6 text-indigo-500",
    items: funnelStages,
  },
];

// Actividad reciente (mock - en implementación real se cargaría del historial del usuario)
const recentActivities = [
  {
    id: "1",
    name: "Blog Post: 10 SEO Tips for 2025",
    timestamp: "2 hours ago",
    category: "blog",
    subcategory: "blog-post",
  },
  {
    id: "2",
    name: "Social Media Post for Product Launch",
    timestamp: "5 hours ago",
    category: "social",
    subcategory: "social-post",
  },
  {
    id: "3",
    name: "Email Newsletter Template",
    timestamp: "Yesterday",
    category: "email",
    subcategory: "newsletter",
  },
];

export default function AIContentHubPageNew() {
  // Estados para la UI
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategoryType, setSelectedCategoryType] = useState<
    string | null
  >(null);

  // Estados para la navegación
  const [currentCategory, setCurrentCategory] =
    useState<ContentCategory | null>(null);
  const [currentSubcategory, setCurrentSubcategory] =
    useState<ContentSubcategory | null>(null);

  // Estados para los formularios
  const [formValues, setFormValues] = useState<Record<string, any>>({});

  // Estados para la generación de contenido
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState("");

  // Estados para los diálogos
  const [showSubcategoryDialog, setShowSubcategoryDialog] = useState(false);
  const [showGenerationDialog, setShowGenerationDialog] = useState(false);

  // Manejar la búsqueda
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Manejar la selección de categoría principal
  const handleCategoryTypeSelect = (categoryType: string) => {
    setSelectedCategoryType(categoryType);
    setCurrentCategory(null);
    setCurrentSubcategory(null);
  };

  // Manejar la selección de categoría
  const handleCategorySelect = (category: ContentCategory) => {
    setCurrentCategory(category);
    setCurrentSubcategory(null);
  };

  // Manejar la selección de subcategoría
  const handleSubcategorySelect = (subcategory: ContentSubcategory) => {
    setCurrentSubcategory(subcategory);
    setFormValues({});
    setShowSubcategoryDialog(true);
  };

  // Manejar la selección de herramienta destacada
  const handleFeaturedToolSelect = (tool: (typeof featuredTools)[0]) => {
    // Encontrar la categoría y subcategoría correspondientes
    const category = contentCategories.find((c) => c.id === tool.category);
    const subcategory = category?.subcategories.find(
      (s) => s.id === tool.subcategory,
    );

    if (category && subcategory) {
      setCurrentCategory(category);
      setCurrentSubcategory(subcategory);
      setFormValues({});
      setShowSubcategoryDialog(true);
    }
  };

  // Manejar cambios en los campos del formulario
  const handleInputChange = (id: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  // Generar contenido
  const handleGenerateContent = () => {
    if (!currentSubcategory) return;

    // Verificar campos requeridos
    const questions = getQuestionsForSubcategory(currentSubcategory.id);
    const requiredQuestions = questions.filter((q) => q.required);
    const missingRequired = requiredQuestions.filter((q) => !formValues[q.id]);

    if (missingRequired.length > 0) {
      alert(
        "Por favor completa todos los campos requeridos antes de continuar.",
      );
      return;
    }

    setIsGenerating(true);
    setShowSubcategoryDialog(false);
    setShowGenerationDialog(true);

    // Simulación de generación de contenido
    setTimeout(async () => {
      const contentType = currentSubcategory.name;
      const categoryName = currentCategory?.name || "";

      // Contenido de ejemplo para demostración
      const sampleContent = `# ${contentType}

## Descripción
Este es un contenido de ejemplo generado para la subcategoría: ${contentType}
dentro de la categoría: ${categoryName}.

## Detalles
- Tipo de contenido: ${contentType}
- Categoría: ${categoryName}
- Fecha de generación: ${new Date().toLocaleDateString()}

## Contenido
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod,
nunc sit amet aliquam lacinia, nisl nisl aliquam nisl, vitae aliquam nisl
nisl sit amet nisl. Sed euismod, nunc sit amet aliquam lacinia, nisl nisl
aliquam nisl, vitae aliquam nisl nisl sit amet nisl.

En una implementación real, este contenido sería generado por un modelo
de lenguaje avanzado como GPT-4, Claude, o Google Gemini, utilizando
las respuestas específicas proporcionadas en el formulario.

## Próximos pasos
1. Revisa el contenido generado
2. Edita según sea necesario
3. Utiliza este contenido en tu estrategia de marketing
`;

      setGeneratedContent(sampleContent);
      setIsGenerating(false);
    }, 3000);
  };

  // Renderizar el formulario de preguntas para la subcategoría seleccionada
  const renderTemplateQuestions = () => {
    if (!currentSubcategory) return null;

    const questions = getQuestionsForSubcategory(currentSubcategory.id);

    if (questions.length === 0) {
      return (
        <div className="py-4 text-center text-muted-foreground">
          No hay preguntas disponibles para esta plantilla.
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {questions.map((question) => (
          <div key={question.id} className="space-y-2">
            <Label className="text-base font-semibold">
              {question.label}
              {question.required && (
                <span className="text-destructive ml-1">*</span>
              )}
            </Label>

            {question.description && (
              <p className="text-sm text-muted-foreground mb-2">
                {question.description}
              </p>
            )}

            {question.type === "text" && (
              <Input
                placeholder={question.placeholder}
                value={formValues[question.id] || ""}
                onChange={(e) => handleInputChange(question.id, e.target.value)}
              />
            )}

            {question.type === "textarea" && (
              <Textarea
                placeholder={question.placeholder}
                value={formValues[question.id] || ""}
                onChange={(e) => handleInputChange(question.id, e.target.value)}
                className="min-h-[100px]"
              />
            )}

            {question.type === "radio" && question.options && (
              <RadioGroup
                value={formValues[question.id] || ""}
                onValueChange={(value) => handleInputChange(question.id, value)}
              >
                <div className="space-y-2">
                  {question.options.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem
                        value={option.value}
                        id={`${question.id}-${option.value}`}
                      />
                      <Label htmlFor={`${question.id}-${option.value}`}>
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            )}

            {question.type === "checkbox" && question.options && (
              <div className="space-y-2">
                {question.options.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`${question.id}-${option.value}`}
                      checked={
                        formValues[question.id]?.includes(option.value) || false
                      }
                      onCheckedChange={(checked) => {
                        const currentValues = formValues[question.id] || [];
                        if (checked) {
                          handleInputChange(question.id, [
                            ...currentValues,
                            option.value,
                          ]);
                        } else {
                          handleInputChange(
                            question.id,
                            currentValues.filter(
                              (v: string) => v !== option.value,
                            ),
                          );
                        }
                      }}
                    />
                    <Label htmlFor={`${question.id}-${option.value}`}>
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Renderizar las herramientas destacadas
  const renderFeaturedTools = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {featuredTools.map((tool) => (
          <Card key={tool.id} className="border overflow-hidden">
            <CardHeader className="pb-2">
              <div
                className={`p-4 rounded-md w-12 h-12 flex items-center justify-center ${tool.bgColor}`}
              >
                {renderIcon(tool.iconName, "h-10 w-10 text-primary")}
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <h3 className="font-semibold text-lg">{tool.name}</h3>
              <p className="text-sm text-muted-foreground">
                {tool.description}
              </p>
            </CardContent>
            <CardFooter>
              <Button
                variant="ghost"
                className="text-primary flex items-center p-0 h-8"
                onClick={() => handleFeaturedToolSelect(tool)}
              >
                Use Tool <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  };

  // Renderizar las categorías para navegar
  const renderBrowseCategories = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {browseCategories.map((category) => (
          <Card
            key={category.id}
            className="hover:bg-muted/20 transition-colors cursor-pointer"
            onClick={() => handleCategoryTypeSelect(category.id)}
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="flex space-x-4 items-center">
                <div className="bg-primary/10 p-2 rounded-md">
                  {renderIcon(category.iconName, category.iconClass)}
                </div>
                <div>
                  <CardTitle className="text-xl">{category.name}</CardTitle>
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription className="text-sm">
                {category.description}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Renderizar las subcategorías
  const renderContentTypesList = () => {
    const categories =
      selectedCategoryType === "content-types"
        ? contentCategories
        : selectedCategoryType === "marketing-functions"
          ? marketingFunctions
          : selectedCategoryType === "funnel-stage"
            ? funnelStages
            : [];

    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            className="flex items-center p-2"
            onClick={() => setSelectedCategoryType(null)}
          >
            <ChevronLeft className="h-4 w-4 mr-1" /> Back to Categories
          </Button>
        </div>

        <h2 className="text-2xl font-bold">
          {selectedCategoryType === "content-types" && "Content Types"}
          {selectedCategoryType === "marketing-functions" &&
            "Marketing Functions"}
          {selectedCategoryType === "funnel-stage" && "Funnel Stage"}
        </h2>

        <p className="text-muted-foreground">
          {selectedCategoryType === "content-types" &&
            "Choose the type of marketing content you need to create"}
          {selectedCategoryType === "marketing-functions" &&
            "Select based on your marketing strategy focus"}
          {selectedCategoryType === "funnel-stage" &&
            "Choose content based on your customer journey stage"}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {categories.map((category) => (
            <Card
              key={category.id}
              className="hover:bg-accent/5 transition-colors border cursor-pointer"
              onClick={() => handleCategorySelect(category)}
            >
              <CardHeader className="px-4 py-3 flex flex-row items-start space-x-4">
                <div className="bg-primary/10 p-2 rounded-md">
                  {renderIcon(category.icon)}
                </div>
                <div>
                  <div className="flex items-center">
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                    {category.badge && (
                      <Badge
                        variant="outline"
                        className="ml-2 text-xs px-2 py-0 h-5"
                      >
                        {category.badge}
                      </Badge>
                    )}
                  </div>
                  <CardDescription className="text-sm mt-1">
                    {category.description}
                  </CardDescription>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // Renderizar subcategorías de la categoría seleccionada
  const renderSubcategories = () => {
    if (!currentCategory) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            className="flex items-center p-2"
            onClick={() => {
              setCurrentCategory(null);
            }}
          >
            <ChevronLeft className="h-4 w-4 mr-1" /> Back to{" "}
            {selectedCategoryType === "content-types"
              ? "Content Types"
              : selectedCategoryType === "marketing-functions"
                ? "Marketing Functions"
                : selectedCategoryType === "funnel-stage"
                  ? "Funnel Stage"
                  : "Categories"}
          </Button>
        </div>

        <div>
          <h2 className="text-2xl font-bold">{currentCategory.name}</h2>
          <p className="text-muted-foreground mt-1">
            {currentCategory.description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {currentCategory.subcategories.map((subcategory) => (
            <Card
              key={subcategory.id}
              className="hover:bg-accent/5 transition-colors border cursor-pointer"
              onClick={() => handleSubcategorySelect(subcategory)}
            >
              <CardHeader className="px-4 py-4">
                <CardTitle className="flex justify-between items-center">
                  <span>{subcategory.name}</span>
                  <Badge variant="outline" className="text-xs px-2 py-0 h-5">
                    AI Powered
                  </Badge>
                </CardTitle>
                <CardDescription>{subcategory.description}</CardDescription>
              </CardHeader>
              <CardFooter className="px-4 py-2 border-t">
                <Button
                  variant="ghost"
                  className="text-primary flex items-center p-0 h-8"
                >
                  Select <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // Renderizar la actividad reciente
  const renderRecentActivity = () => {
    if (recentActivities.length === 0) {
      return (
        <div className="text-center py-4 border rounded-md">
          <p className="text-muted-foreground">No recent activity found.</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {recentActivities.map((activity) => (
          <Card
            key={activity.id}
            className="hover:bg-muted/20 transition-colors cursor-pointer"
          >
            <CardHeader className="p-3">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-primary" />
                <div>
                  <CardTitle className="text-sm font-medium">
                    {activity.name}
                  </CardTitle>
                  <CardDescription className="text-xs">
                    {activity.timestamp}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  };

  // Renderizar el diálogo de generación de contenido
  const renderGenerationDialog = () => {
    return (
      <Dialog
        open={showGenerationDialog}
        onOpenChange={setShowGenerationDialog}
      >
        <DialogContent className="max-w-6xl">
          <DialogHeader>
            <DialogTitle>
              {isGenerating ? "Generando contenido..." : "Contenido generado"}
            </DialogTitle>
            <DialogDescription>
              {isGenerating
                ? "Los agentes de IA están creando tu contenido personalizado..."
                : "Aquí tienes el contenido generado basado en tus respuestas."}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
            {/* Panel izquierdo: Conversación de agentes */}
            <div className="border rounded-md p-4 bg-muted/20">
              <h3 className="text-lg font-medium mb-3">
                Agentes de IA trabajando
              </h3>

              {isGenerating ? (
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 text-blue-700 p-2 rounded-full">
                      <Code className="h-4 w-4" />
                    </div>
                    <div className="bg-blue-50 p-3 rounded-md">
                      <p className="text-sm font-medium">
                        Agente de Investigación
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Analizando el tema principal y recopilando datos
                        relevantes...
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-purple-100 text-purple-700 p-2 rounded-full">
                      <Target className="h-4 w-4" />
                    </div>
                    <div className="bg-purple-50 p-3 rounded-md">
                      <p className="text-sm font-medium">Agente de Audiencia</p>
                      <p className="text-sm text-muted-foreground">
                        Adaptando el contenido para tu audiencia objetivo...
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="h-8 w-8 text-primary animate-spin" />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 text-blue-700 p-2 rounded-full">
                      <Code className="h-4 w-4" />
                    </div>
                    <div className="bg-blue-50 p-3 rounded-md">
                      <p className="text-sm font-medium">
                        Agente de Investigación
                      </p>
                      <p className="text-sm text-muted-foreground">
                        He analizado el tema y encontrado información relevante
                        para crear contenido optimizado.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-purple-100 text-purple-700 p-2 rounded-full">
                      <Target className="h-4 w-4" />
                    </div>
                    <div className="bg-purple-50 p-3 rounded-md">
                      <p className="text-sm font-medium">Agente de Audiencia</p>
                      <p className="text-sm text-muted-foreground">
                        He adaptado el tono y el lenguaje para resonar con tu
                        audiencia objetivo.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-green-100 text-green-700 p-2 rounded-full">
                      <FileText className="h-4 w-4" />
                    </div>
                    <div className="bg-green-50 p-3 rounded-md">
                      <p className="text-sm font-medium">Agente de Escritura</p>
                      <p className="text-sm text-muted-foreground">
                        He estructurado el contenido para maximizar su impacto y
                        legibilidad.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-amber-100 text-amber-700 p-2 rounded-full">
                      <Check className="h-4 w-4" />
                    </div>
                    <div className="bg-amber-50 p-3 rounded-md">
                      <p className="text-sm font-medium">Agente de Revisión</p>
                      <p className="text-sm text-muted-foreground">
                        He verificado el contenido para asegurar su calidad y
                        coherencia.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Panel derecho: Contenido generado */}
            <div className="border rounded-md p-4">
              <h3 className="text-lg font-medium mb-3">Contenido generado</h3>

              {isGenerating ? (
                <div className="flex flex-col items-center justify-center h-64">
                  <Loader2 className="h-10 w-10 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">
                    Creando tu contenido personalizado...
                  </p>
                </div>
              ) : (
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap bg-muted p-4 rounded-md overflow-auto max-h-96">
                    {generatedContent}
                  </pre>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            {!isGenerating && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setShowGenerationDialog(false)}
                >
                  Cerrar
                </Button>
                <Button>
                  <FileText className="mr-2 h-4 w-4" />
                  Copiar contenido
                </Button>
                <Button variant="default">
                  <Sparkles className="mr-2 h-4 w-4" />
                  Editar y mejorar
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // Renderizar el diálogo de subcategoría
  const renderSubcategoryDialog = () => {
    return (
      <Dialog
        open={showSubcategoryDialog}
        onOpenChange={setShowSubcategoryDialog}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="text-2xl">
              {currentSubcategory?.name}
              {currentCategory && (
                <Badge variant="outline" className="ml-2">
                  {currentCategory.name}
                </Badge>
              )}
            </DialogTitle>
            <DialogDescription>
              {currentSubcategory?.description}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <h3 className="text-lg font-medium mb-4">
              Personaliza tu contenido
            </h3>
            {renderTemplateQuestions()}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSubcategoryDialog(false)}
            >
              Cancelar
            </Button>
            <Button onClick={handleGenerateContent}>
              <Sparkles className="mr-2 h-4 w-4" />
              Generar Contenido
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <DashboardLayoutWrapper>
      <div className="container mx-auto py-6 max-w-7xl">
        {/* Barra de búsqueda */}
        <div className="relative max-w-xl mx-auto mb-8">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search for content types, formats, or tools..."
            className="pl-10 h-12"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-2"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Contenido principal */}
        <div className="space-y-12">
          {/* Herramientas destacadas */}
          {!selectedCategoryType && !currentCategory && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Featured AI Tools</h2>
                <Button variant="ghost">
                  View All <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
              {renderFeaturedTools()}
            </div>
          )}

          {/* Navegación por categoría */}
          {!selectedCategoryType && !currentCategory && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Browse by Category</h2>
              {renderBrowseCategories()}
            </div>
          )}

          {/* Lista de tipos de contenido */}
          {selectedCategoryType && !currentCategory && renderContentTypesList()}

          {/* Lista de subcategorías */}
          {currentCategory && renderSubcategories()}

          {/* Actividad reciente */}
          {!selectedCategoryType && !currentCategory && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Recent Activity</h2>
              {renderRecentActivity()}
            </div>
          )}
        </div>

        {/* Diálogos */}
        {renderSubcategoryDialog()}
        {renderGenerationDialog()}
      </div>
    </DashboardLayoutWrapper>
  );
}
