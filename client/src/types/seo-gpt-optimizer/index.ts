/**
 * SEO & GPT Optimizer™ - TypeScript Types
 * Type definitions for the SEO GPT Optimizer system
 */

// Enums
export enum ProjectStatus {
  DRAFT = 'draft',
  RESEARCHING = 'researching',
  WRITING = 'writing',
  OPTIMIZING = 'optimizing',
  COMPLETED = 'completed',
  PUBLISHED = 'published'
}

export enum ContentType {
  ARTICLE = 'article',
  GUIDE = 'guide',
  TUTORIAL = 'tutorial',
  COMPARISON = 'comparison',
  LIST = 'list',
  FAQ = 'faq'
}

export enum IntentType {
  INFORMATIONAL = 'informational',
  COMMERCIAL = 'commercial',
  TRANSACTIONAL = 'transactional',
  NAVIGATIONAL = 'navigational'
}

// Research Types
export interface ResearchRequest {
  topic: string;
  target_language: string;
  include_reddit: boolean;
  include_quora: boolean;
  // Project linking
  project_id?: string;
  // Geographic targeting
  target_country?: string;
  // Additional sources
  include_news?: boolean;
  // Competitive analysis
  competitor_domains?: string[];
}

export interface IntentAnalysis {
  intent_type: IntentType;
  intent_confidence: number;
  user_motivation: string;
  search_stage: 'awareness' | 'consideration' | 'decision';
  content_type_preference: string;
  target_audience: string;
  pain_points: string[];
  search_variations: string[];
  related_topics: string[];
  optimal_content_length: 'corto' | 'medio' | 'largo';
  preferred_tone: 'formal' | 'informal' | 'técnico' | 'conversacional';
}

export interface GoogleResult {
  position: number;
  title: string;
  link: string;
  snippet: string;
  displayed_link: string;
  date: string;
  content_preview: string;
  domain: string;
  title_length: number;
  snippet_length: number;
}

export interface SerpFeatures {
  people_also_ask: Array<{ question: string }>;
  related_searches: string[];
  knowledge_graph: Record<string, any>;
  featured_snippet: Record<string, any>;
}

export interface GoogleResults {
  status: string;
  query: string;
  total_results: number;
  results: GoogleResult[];
  serp_features: SerpFeatures;
  search_metadata: {
    search_time: number;
    total_results_count: number;
  };
}

export interface SocialInsight {
  title?: string;
  question?: string;
  link: string;
  snippet?: string;
  answer_preview?: string;
  subreddit?: string;
  discussion_points?: string[];
  question_type?: string;
}

export interface SocialInsights {
  reddit?: {
    status: string;
    total_results: number;
    insights: SocialInsight[];
  };
  quora?: {
    status: string;
    total_results: number;
    insights: SocialInsight[];
  };
}

export interface GPTReference {
  status: string;
  topic: string;
  response_text: string;
  response_length: number;
  word_count: number;
  structure_analysis: Record<string, any>;
  key_phrases: string[];
  response_tone: string;
}

export interface EntitiesAndQuestions {
  status: string;
  topic: string;
  entities: {
    people: string[];
    places: string[];
    concepts: string[];
    brands: string[];
    products: string[];
    organizations: string[];
  };
  common_questions: string[];
  technical_terms: string[];
  related_concepts: string[];
  semantic_keywords: string[];
}

export interface ContentOpportunities {
  status: string;
  topic: string;
  content_gaps: string[];
  unique_angles: string[];
  recommended_content_types: string[];
  differentiation_opportunities: string[];
  target_keywords: string[];
  content_depth_recommendation: 'superficial' | 'medio' | 'profundo';
  estimated_competition_level: 'bajo' | 'medio' | 'alto';
}

export interface ResearchResults {
  status: string;
  topic: string;
  target_language: string;
  processing_time: number;
  timestamp: number;
  intent_analysis: IntentAnalysis;
  google_results: GoogleResults;
  social_insights: SocialInsights;
  gpt_reference: GPTReference;
  entities_and_questions: EntitiesAndQuestions;
  content_opportunities: ContentOpportunities;
  research_summary: {
    topic: string;
    research_confidence: number;
    key_insights: string[];
    recommended_approach: Record<string, any>;
    priority_questions: string[];
    priority_keywords: string[];
  };
}

// GPT Rank Types
export interface ComponentScores {
  semantic_similarity: number;
  logical_coherence: number;
  authority_signals: number;
  citability_score: number;
  clarity_score: number;
  completeness_score: number;
}

export interface ContentStats {
  word_count: number;
  character_count: number;
  paragraph_count: number;
  sentence_count: number;
  avg_words_per_sentence?: number;
  avg_sentences_per_paragraph?: number;
  reading_time_minutes?: number;
}

export interface ImprovementSuggestion {
  title: string;
  description: string;
  actions: string[];
  examples?: string[];
  component: string;
  current_score: number;
  priority: 'high' | 'medium' | 'low';
  impact: 'high' | 'medium' | 'low';
}

export interface GPTRankAnalysis {
  status: string;
  gpt_rank_score: number;
  score_grade: string;
  confidence_level: 'high' | 'medium' | 'low';
  component_scores: ComponentScores;
  scoring_weights: Record<string, number>;
  content_stats: ContentStats;
  improvement_suggestions: ImprovementSuggestion[];
  analysis_timestamp: number;
}

// Project Types
export interface SEOGPTProject {
  project_id: string;
  user_id?: string;
  title: string;
  topic: string;
  target_language: string;
  content_type: ContentType;
  status: ProjectStatus;
  content_text?: string;
  content_length: number;
  word_count: number;
  current_gpt_rank_score: number;
  target_gpt_rank_score: number;
  best_gpt_rank_score: number;
  seo_score: number;
  target_keywords?: string[];
  created_at: string;
  updated_at: string;
  completed_at?: string;
  published_at?: string;
}

export interface ProjectCreateRequest {
  title: string;
  topic: string;
  target_language: string;
  content_type: ContentType;
  target_gpt_rank_score: number;
  user_id?: string;
}

export interface ContentAnalysisRequest {
  project_id: string;
  content: string;
  analysis_type: string;
}

// Project Content Types
export interface ProjectContent {
  content_id: string;
  title: string;
  content_type: string; // blog, article, research, guide
  status: string; // draft, published, archived
  content_text?: string;
  content_html?: string;
  word_count: number;
  content_length: number;
  current_gpt_rank_score: number;
  target_gpt_rank_score: number;
  seo_score: number;
  target_keywords?: string[];
  meta_description?: string;
  slug?: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
}

export interface ProjectContentCreateRequest {
  title: string;
  content_type?: string;
  content_text?: string;
  content_html?: string;
  target_keywords?: string[];
  meta_description?: string;
  slug?: string;
}

export interface ProjectContentUpdateRequest {
  title?: string;
  content_text?: string;
  content_html?: string;
  status?: string;
  target_keywords?: string[];
  meta_description?: string;
  slug?: string;
  current_gpt_rank_score?: number;
  seo_score?: number;
}

// API Response Types
export interface APIResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  error_message?: string;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: string;
}

// Dashboard Types
export interface DashboardStats {
  total_projects: number;
  active_projects: number;
  avg_gpt_rank_score: number;
  total_research_conducted: number;
  improvement_this_month: number;
}

export interface RecentActivity {
  id: string;
  type: 'research' | 'analysis' | 'project_created' | 'score_improved';
  title: string;
  description: string;
  timestamp: string;
  project_id?: string;
}

// Chart Data Types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
}

export interface ScoreHistoryData {
  project_id: string;
  scores: Array<{
    score: number;
    timestamp: string;
    version: number;
  }>;
}

// Filter and Search Types
export interface ProjectFilters {
  status?: ProjectStatus[];
  content_type?: ContentType[];
  score_range?: [number, number];
  date_range?: [string, string];
  search_query?: string;
}

export interface SortOptions {
  field: 'created_at' | 'updated_at' | 'gpt_rank_score' | 'title';
  direction: 'asc' | 'desc';
}
