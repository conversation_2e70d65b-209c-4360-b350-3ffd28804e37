/**
 * SEO & GPT Optimizer™ - API Service
 * Service for communicating with the SEO GPT Optimizer backend
 */

import {
  ResearchRequest,
  ResearchResults,
  ProjectCreateRequest,
  SEOGPTProject,
  ContentAnalysisRequest,
  GPTRankAnalysis,
  APIResponse,
  ProjectFilters,
  SortOptions,
  ProjectContent,
  ProjectContentCreateRequest,
  ProjectContentUpdateRequest
} from '../../types/seo-gpt-optimizer';

const API_BASE_URL = import.meta.env.VITE_API_BASE || 'http://localhost:8001';  // TODO EL BACKEND CORRE EN PUERTO 8001
const SEO_GPT_BASE = `${API_BASE_URL}/api/v1/seo-gpt`;

class SEOGPTOptimizerAPI {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    try {
      const response = await fetch(`${SEO_GPT_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return {
        status: 'success',
        data,
      };
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      return {
        status: 'error',
        error_message: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Research Methods
  async conductResearch(request: ResearchRequest): Promise<APIResponse<ResearchResults>> {
    return this.request<ResearchResults>('/research', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // Project Methods
  async createProject(request: ProjectCreateRequest): Promise<APIResponse<{ project_id: string; project: Partial<SEOGPTProject> }>> {
    return this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getProjects(
    filters?: ProjectFilters,
    sort?: SortOptions,
    limit: number = 20,
    offset: number = 0
  ): Promise<APIResponse<{ projects: SEOGPTProject[]; total: number }>> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    if (filters?.status?.length) {
      params.append('status', filters.status.join(','));
    }
    if (filters?.search_query) {
      params.append('search', filters.search_query);
    }
    if (sort) {
      params.append('sort_field', sort.field);
      params.append('sort_direction', sort.direction);
    }

    return this.request(`/projects?${params.toString()}`);
  }

  async getProject(projectId: string): Promise<APIResponse<SEOGPTProject>> {
    return this.request(`/projects/${projectId}`);
  }

  async updateProject(
    projectId: string,
    updates: Partial<SEOGPTProject>
  ): Promise<APIResponse<SEOGPTProject>> {
    const response = await this.request(`/projects/${projectId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });

    // Handle nested response structure from backend
    if (response.status === 'success' && response.data && typeof response.data === 'object' && 'data' in response.data) {
      return {
        ...response,
        data: (response.data as any).data
      } as APIResponse<SEOGPTProject>;
    }

    return response as APIResponse<SEOGPTProject>;
  }

  async deleteProject(projectId: string): Promise<APIResponse<void>> {
    return this.request(`/projects/${projectId}`, {
      method: 'DELETE',
    });
  }

  // Project Content Methods
  async getProjectContents(
    projectId: string,
    contentType?: string,
    status?: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<APIResponse<{ contents: ProjectContent[]; total: number }>> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    if (contentType) {
      params.append('content_type', contentType);
    }
    if (status) {
      params.append('status', status);
    }

    return this.request(`/projects/${projectId}/contents?${params.toString()}`);
  }

  async createProjectContent(
    projectId: string,
    request: ProjectContentCreateRequest
  ): Promise<APIResponse<ProjectContent>> {
    const url = `/projects/${projectId}/contents`;
    console.log("🔥 API DEBUG - Full URL:", `${SEO_GPT_BASE}${url}`);
    console.log("🔥 API DEBUG - Project ID:", projectId);
    console.log("🔥 API DEBUG - Request payload:", request);

    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getProjectContent(
    projectId: string,
    contentId: string
  ): Promise<APIResponse<ProjectContent>> {
    return this.request(`/projects/${projectId}/contents/${contentId}`);
  }

  async updateProjectContent(
    projectId: string,
    contentId: string,
    updates: ProjectContentUpdateRequest
  ): Promise<APIResponse<ProjectContent>> {
    return this.request(`/projects/${projectId}/contents/${contentId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });
  }

  async deleteProjectContent(
    projectId: string,
    contentId: string
  ): Promise<APIResponse<void>> {
    return this.request(`/projects/${projectId}/contents/${contentId}`, {
      method: 'DELETE',
    });
  }

  async getProjectResearch(
    projectId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<APIResponse<{ research: any[]; total: number }>> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });

    return this.request(`/projects/${projectId}/research?${params.toString()}`);
  }

  // Content Analysis Methods
  async analyzeContent(request: ContentAnalysisRequest): Promise<APIResponse<GPTRankAnalysis>> {
    return this.request<GPTRankAnalysis>('/content/analyze', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getProjectScore(projectId: string): Promise<APIResponse<{
    project_id: string;
    current_score: number;
    best_score: number;
    target_score: number;
    score_grade: string;
    confidence_level: string;
    latest_analysis: any;
    score_history: Array<{
      score: number;
      change: number;
      grade: string;
      created_at: string;
    }>;
  }>> {
    return this.request(`/projects/${projectId}/score`);
  }

  // Real-time Analysis (for content editor)
  async analyzeContentRealtime(
    content: string,
    topic: string,
    debounceMs: number = 1000
  ): Promise<APIResponse<GPTRankAnalysis>> {
    // Simple debouncing mechanism
    return new Promise((resolve) => {
      setTimeout(async () => {
        const result = await this.request<GPTRankAnalysis>('/content/analyze', {
          method: 'POST',
          body: JSON.stringify({
            content,
            topic,
            analysis_type: 'quick'
          }),
        });
        resolve(result);
      }, debounceMs);
    });
  }

  // Dashboard Methods
  async getDashboardStats(): Promise<APIResponse<{
    total_projects: number;
    active_projects: number;
    avg_gpt_rank_score: number;
    total_research_conducted: number;
    improvement_this_month: number;
    recent_activities: Array<{
      id: string;
      type: string;
      title: string;
      description: string;
      timestamp: string;
      project_id?: string;
    }>;
  }>> {
    return this.request('/dashboard/stats');
  }

  async getScoreHistory(
    projectId: string,
    days: number = 30
  ): Promise<APIResponse<Array<{
    score: number;
    timestamp: string;
    version: number;
    trigger_event?: string;
  }>>> {
    return this.request(`/projects/${projectId}/score/history?days=${days}`);
  }

  // Export Methods
  async exportProject(
    projectId: string,
    format: 'html' | 'markdown' | 'pdf' | 'docx'
  ): Promise<APIResponse<{ download_url: string }>> {
    return this.request(`/projects/${projectId}/export?format=${format}`, {
      method: 'POST',
    });
  }

  async exportAnalysis(
    projectId: string,
    analysisId: string,
    format: 'json' | 'csv' | 'pdf'
  ): Promise<APIResponse<{ download_url: string }>> {
    return this.request(`/projects/${projectId}/analysis/${analysisId}/export?format=${format}`, {
      method: 'POST',
    });
  }

  // Batch Operations
  async batchAnalyzeProjects(
    projectIds: string[]
  ): Promise<APIResponse<Array<{ project_id: string; analysis: GPTRankAnalysis }>>> {
    return this.request('/content/batch-analyze', {
      method: 'POST',
      body: JSON.stringify({ project_ids: projectIds }),
    });
  }

  // Search and Suggestions
  async searchProjects(
    query: string,
    filters?: ProjectFilters
  ): Promise<APIResponse<{ projects: SEOGPTProject[]; total: number }>> {
    const params = new URLSearchParams({ q: query });
    
    if (filters?.status?.length) {
      params.append('status', filters.status.join(','));
    }
    if (filters?.content_type?.length) {
      params.append('content_type', filters.content_type.join(','));
    }

    return this.request(`/projects/search?${params.toString()}`);
  }

  async getTopicSuggestions(
    partial: string
  ): Promise<APIResponse<{ suggestions: string[] }>> {
    return this.request(`/suggestions/topics?q=${encodeURIComponent(partial)}`);
  }

  async getKeywordSuggestions(
    topic: string
  ): Promise<APIResponse<{ keywords: string[] }>> {
    return this.request(`/suggestions/keywords?topic=${encodeURIComponent(topic)}`);
  }

  // Health Check
  async healthCheck(): Promise<APIResponse<{ status: string; timestamp: string }>> {
    return this.request('/health');
  }
}

// Create singleton instance
export const seoGptAPI = new SEOGPTOptimizerAPI();

// Export individual methods for easier importing
export const {
  conductResearch,
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  getProjectContents,
  createProjectContent,
  getProjectContent,
  updateProjectContent,
  deleteProjectContent,
  getProjectResearch,
  analyzeContent,
  analyzeContentRealtime,
  getProjectScore,
  getDashboardStats,
  getScoreHistory,
  exportProject,
  searchProjects,
  getTopicSuggestions,
  getKeywordSuggestions,
  healthCheck
} = seoGptAPI;
