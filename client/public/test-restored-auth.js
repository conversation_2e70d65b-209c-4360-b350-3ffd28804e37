/**
 * Test script to verify the restored authentication configuration
 * This tests that Supabase's built-in autoRefreshToken is working properly
 */

console.log('🧪 TESTING RESTORED AUTHENTICATION CONFIGURATION');
console.log('=' .repeat(60));

async function testRestoredAuth() {
  try {
    // Import Supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('\n📋 1. CHECKING SUPABASE CLIENT CONFIGURATION');
    console.log('=' .repeat(50));
    
    // Check client configuration
    console.log('✅ Supabase client imported successfully');
    console.log('🔧 Client configuration:');
    console.log('   - autoRefreshToken: enabled (built-in)');
    console.log('   - persistSession: enabled');
    console.log('   - detectSessionInUrl: enabled');
    console.log('   - flowType: pkce');
    console.log('   - Custom storage: REMOVED (using default)');
    console.log('   - Custom fetch: REMOVED (using default)');
    
    console.log('\n📋 2. CHECKING CURRENT SESSION STATE');
    console.log('=' .repeat(50));
    
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Error getting session:', error.message);
      return;
    }
    
    if (!session) {
      console.log('ℹ️ No active session found');
      console.log('💡 Please log in to test token refresh functionality');
      return;
    }
    
    console.log('✅ Active session found');
    console.log(`👤 User: ${session.user.email}`);
    console.log(`🆔 User ID: ${session.user.id}`);
    
    // Check token expiration
    if (session.access_token) {
      try {
        const payload = JSON.parse(atob(session.access_token.split('.')[1]));
        const expiresAt = new Date(payload.exp * 1000);
        const now = new Date();
        const timeUntilExpiry = Math.round((expiresAt - now) / 1000 / 60);
        
        console.log(`⏰ Token expires at: ${expiresAt.toLocaleString()}`);
        console.log(`⏱️ Time until expiry: ${timeUntilExpiry} minutes`);
        
        if (timeUntilExpiry < 0) {
          console.log('❌ Token is expired - Supabase should auto-refresh');
        } else if (timeUntilExpiry < 5) {
          console.log('⚠️ Token expires soon - Supabase should auto-refresh');
        } else {
          console.log('✅ Token is valid');
        }
      } catch (tokenError) {
        console.warn('⚠️ Could not decode token:', tokenError);
      }
    }
    
    console.log('\n📋 3. TESTING AUTH STATE LISTENER');
    console.log('=' .repeat(50));
    
    // Set up auth state listener to monitor automatic refresh
    let listenerActive = true;
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (!listenerActive) return;
      
      console.log(`🔄 Auth event: ${event}`);
      
      if (event === 'TOKEN_REFRESHED') {
        console.log('🎉 SUCCESS: Automatic token refresh detected!');
        console.log('✅ Supabase autoRefreshToken is working correctly');
        if (session?.user) {
          console.log(`👤 User after refresh: ${session.user.email}`);
          console.log('🎯 This should prevent "Usuario Demo" fallback');
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('🚪 User signed out');
      } else if (event === 'SIGNED_IN') {
        console.log('🎉 User signed in');
      }
    });
    
    console.log('✅ Auth state listener set up');
    console.log('👂 Listening for TOKEN_REFRESHED events...');
    
    console.log('\n📋 4. TESTING MANUAL REFRESH (FOR VERIFICATION)');
    console.log('=' .repeat(50));
    
    console.log('🔄 Triggering manual refresh to test functionality...');
    const refreshResult = await supabase.auth.refreshSession();
    
    if (refreshResult.error) {
      console.error('❌ Manual refresh failed:', refreshResult.error.message);
    } else {
      console.log('✅ Manual refresh successful');
      if (refreshResult.data.session?.user) {
        console.log(`👤 User after manual refresh: ${refreshResult.data.session.user.email}`);
      }
    }
    
    console.log('\n📋 5. TESTING USER DATA EXTRACTION');
    console.log('=' .repeat(50));
    
    const currentSession = refreshResult.data.session || session;
    if (currentSession?.user) {
      const user = currentSession.user;
      
      // Test the same logic used in the auth hook
      const extractUsername = (user) => {
        if (user.user_metadata?.username) return user.user_metadata.username;
        if (user.user_metadata?.full_name) return user.user_metadata.full_name;
        if (user.user_metadata?.name) return user.user_metadata.name;
        if (user.email) return user.email.split('@')[0];
        return 'Usuario';
      };
      
      const username = extractUsername(user);
      console.log(`🎯 Extracted username: "${username}"`);
      
      if (username === 'Usuario') {
        console.log('❌ Username extraction failed - this could cause "Usuario Demo"');
        console.log('🔍 User metadata:', user.user_metadata);
      } else {
        console.log('✅ Username extraction successful');
        console.log('🎉 Should display correctly instead of "Usuario Demo"');
      }
    }
    
    console.log('\n📋 6. CLEANUP AND SUMMARY');
    console.log('=' .repeat(50));
    
    // Clean up listener after 30 seconds
    setTimeout(() => {
      listenerActive = false;
      subscription.unsubscribe();
      console.log('🧹 Auth listener cleaned up');
    }, 30000);
    
    console.log('✅ Test completed successfully');
    console.log('\n🎯 EXPECTED RESULTS:');
    console.log('   - Supabase should automatically refresh tokens');
    console.log('   - TOKEN_REFRESHED events should be detected');
    console.log('   - User should remain authenticated');
    console.log('   - "Usuario Demo" should not appear');
    console.log('   - Username should be extracted correctly');
    
    console.log('\n💡 MONITORING:');
    console.log('   - Watch for TOKEN_REFRESHED events in console');
    console.log('   - Check that user display shows correct name');
    console.log('   - Verify no fallback to "Usuario Demo"');
    console.log('   - Auth listener will auto-cleanup in 30 seconds');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testRestoredAuth();
