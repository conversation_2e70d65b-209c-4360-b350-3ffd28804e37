/**
 * 🧪 VERIFICATION SCRIPT FOR RESTORED AUTHENTICATION
 * Run this in browser console to verify the fixes are working
 */

console.log('🎯 VERIFYING RESTORED AUTHENTICATION SYSTEM');
console.log('=' .repeat(60));

async function verifyRestoredAuth() {
  try {
    console.log('\n📋 1. CHECKING SUPABASE CLIENT CONFIGURATION');
    console.log('=' .repeat(50));
    
    // Import Supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('✅ Supabase client imported successfully');
    console.log('🔧 Restored configuration:');
    console.log('   - autoRefreshToken: enabled (built-in)');
    console.log('   - persistSession: enabled');
    console.log('   - detectSessionInUrl: enabled');
    console.log('   - flowType: pkce');
    console.log('   - ✅ REMOVED: Custom storage configuration');
    console.log('   - ✅ REMOVED: Custom fetch implementation');
    console.log('   - ✅ REMOVED: Custom storageKey');
    
    console.log('\n📋 2. CHECKING CURRENT SESSION STATE');
    console.log('=' .repeat(50));
    
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Error getting session:', error.message);
      return;
    }
    
    if (!session) {
      console.log('ℹ️ No active session found');
      console.log('💡 Please log in to test the restored authentication');
      console.log('🔗 Go to: http://localhost:3002/login');
      return;
    }
    
    console.log('✅ Active session found');
    console.log(`👤 User: ${session.user.email}`);
    console.log(`🆔 User ID: ${session.user.id}`);
    
    // Extract username using the same logic as the auth hook
    const extractUsername = (user) => {
      if (user.user_metadata?.username) return user.user_metadata.username;
      if (user.user_metadata?.full_name) return user.user_metadata.full_name;
      if (user.user_metadata?.name) return user.user_metadata.name;
      if (user.email) return user.email.split('@')[0];
      return 'Usuario';
    };
    
    const username = extractUsername(session.user);
    console.log(`🎯 Extracted username: "${username}"`);
    
    if (username === 'Usuario') {
      console.log('❌ Username extraction failed - check user metadata');
      console.log('🔍 User metadata:', session.user.user_metadata);
    } else {
      console.log('✅ Username extraction successful');
      console.log('🎉 Should display correctly (not "Usuario Demo")');
    }
    
    // Check token expiration
    if (session.access_token) {
      try {
        const payload = JSON.parse(atob(session.access_token.split('.')[1]));
        const expiresAt = new Date(payload.exp * 1000);
        const now = new Date();
        const timeUntilExpiry = Math.round((expiresAt - now) / 1000 / 60);
        
        console.log(`⏰ Token expires at: ${expiresAt.toLocaleString()}`);
        console.log(`⏱️ Time until expiry: ${timeUntilExpiry} minutes`);
        
        if (timeUntilExpiry < 0) {
          console.log('❌ Token is expired - Supabase should auto-refresh');
        } else if (timeUntilExpiry < 5) {
          console.log('⚠️ Token expires soon - Supabase should auto-refresh');
        } else {
          console.log('✅ Token is valid');
        }
      } catch (tokenError) {
        console.warn('⚠️ Could not decode token:', tokenError);
      }
    }
    
    console.log('\n📋 3. TESTING AUTH STATE LISTENER');
    console.log('=' .repeat(50));
    
    // Set up auth state listener to monitor automatic refresh
    let listenerActive = true;
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (!listenerActive) return;
      
      console.log(`🔄 Auth event: ${event}`);
      
      if (event === 'TOKEN_REFRESHED') {
        console.log('🎉 SUCCESS: Automatic token refresh detected!');
        console.log('✅ Supabase autoRefreshToken is working correctly');
        if (session?.user) {
          console.log(`👤 User after refresh: ${session.user.email}`);
          console.log('🎯 This prevents "Usuario Demo" fallback');
        }
      } else if (event === 'SIGNED_OUT') {
        console.log('🚪 User signed out');
      } else if (event === 'SIGNED_IN') {
        console.log('🎉 User signed in');
      }
    });
    
    console.log('✅ Auth state listener set up');
    console.log('👂 Listening for TOKEN_REFRESHED events...');
    
    console.log('\n📋 4. TESTING MANUAL REFRESH');
    console.log('=' .repeat(50));
    
    console.log('🔄 Triggering manual refresh to test functionality...');
    const refreshResult = await supabase.auth.refreshSession();
    
    if (refreshResult.error) {
      console.error('❌ Manual refresh failed:', refreshResult.error.message);
    } else {
      console.log('✅ Manual refresh successful');
      if (refreshResult.data.session?.user) {
        console.log(`👤 User after manual refresh: ${refreshResult.data.session.user.email}`);
        const refreshedUsername = extractUsername(refreshResult.data.session.user);
        console.log(`🎯 Username after refresh: "${refreshedUsername}"`);
      }
    }
    
    console.log('\n📋 5. TESTING BACKEND CONNECTIVITY');
    console.log('=' .repeat(50));
    
    // Test backend endpoint with current token
    const currentSession = refreshResult.data.session || session;
    if (currentSession?.access_token) {
      try {
        const response = await fetch('http://localhost:8000/api/v1/health', {
          headers: {
            'Authorization': `Bearer ${currentSession.access_token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          console.log('✅ Backend connectivity successful');
          console.log('🔗 Authentication working end-to-end');
        } else {
          console.log(`⚠️ Backend response: ${response.status}`);
        }
      } catch (fetchError) {
        console.warn('⚠️ Backend connectivity test failed:', fetchError);
      }
    }
    
    console.log('\n📋 6. CLEANUP AND SUMMARY');
    console.log('=' .repeat(50));
    
    // Clean up listener after 30 seconds
    setTimeout(() => {
      listenerActive = false;
      subscription.unsubscribe();
      console.log('🧹 Auth listener cleaned up');
    }, 30000);
    
    console.log('✅ Verification completed successfully');
    console.log('\n🎯 EXPECTED RESULTS:');
    console.log('   - ✅ Supabase automatically refreshes tokens');
    console.log('   - ✅ TOKEN_REFRESHED events detected');
    console.log('   - ✅ User remains authenticated');
    console.log('   - ✅ No "Usuario Demo" fallback');
    console.log('   - ✅ Correct username displayed');
    console.log('   - ✅ Backend connectivity working');
    
    console.log('\n💡 MONITORING:');
    console.log('   - Watch for TOKEN_REFRESHED events in console');
    console.log('   - Check that user display shows correct name');
    console.log('   - Verify no fallback to "Usuario Demo"');
    console.log('   - Auth listener will auto-cleanup in 30 seconds');
    
    console.log('\n🎉 AUTHENTICATION SYSTEM RESTORED SUCCESSFULLY!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Auto-run the verification
verifyRestoredAuth();

// Also make it available globally for manual testing
window.verifyRestoredAuth = verifyRestoredAuth;
