#!/usr/bin/env python3
"""
Diagnostic script to help identify and fix the token generation issue.
This will help determine why fresh tokens are not being generated.
"""

import asyncio
import logging
import sys
import os
import requests
import json
import jwt
from datetime import datetime, timezone

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.supabase import SUPABASE_URL, SUPABASE_ANON_KEY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_supabase_auth_endpoints():
    """Test various Supabase auth endpoints to understand the issue."""
    print("\n🔍 Testing Supabase Authentication Endpoints...")
    print("=" * 60)
    
    endpoints_to_test = [
        ("/auth/v1/settings", "GET", "Auth Settings"),
        ("/auth/v1/health", "GET", "Auth Health"),
        ("/auth/v1/token", "POST", "Token Refresh"),
        ("/auth/v1/user", "GET", "User Info"),
    ]
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Content-Type": "application/json"
    }
    
    for endpoint, method, description in endpoints_to_test:
        try:
            url = f"{SUPABASE_URL}{endpoint}"
            print(f"\n📡 Testing {description} ({method} {endpoint}):")
            
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            elif method == "POST":
                # For token refresh, we'd need a refresh token
                response = requests.post(url, headers=headers, json={}, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ Success: {json.dumps(data, indent=4)[:200]}...")
                except:
                    print(f"   ✅ Success: {response.text[:200]}...")
            else:
                print(f"   ❌ Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

async def generate_test_instructions():
    """Generate step-by-step instructions to get a truly fresh token."""
    print("\n📋 Instructions to Get a TRULY Fresh Token...")
    print("=" * 60)
    
    print("🔧 STEP 1: Clear All Authentication Data")
    print("   1. Open your browser at http://localhost:3002")
    print("   2. Open Developer Tools (F12)")
    print("   3. Go to Application tab")
    print("   4. Clear ALL Local Storage data:")
    print("      - Right-click on 'Local Storage' → Clear")
    print("   5. Clear ALL Session Storage data:")
    print("      - Right-click on 'Session Storage' → Clear")
    print("   6. Clear cookies for localhost:3002")
    print()
    
    print("🔧 STEP 2: Force Complete Logout")
    print("   1. In your app, click Logout (if available)")
    print("   2. Refresh the page (F5)")
    print("   3. Verify you're on the login screen")
    print()
    
    print("🔧 STEP 3: Fresh Login")
    print("   1. Log in again with your credentials")
    print("   2. Wait for complete authentication")
    print("   3. Verify you're logged in and can see the dashboard")
    print()
    
    print("🔧 STEP 4: Extract Fresh Token")
    print("   1. Open Developer Tools (F12)")
    print("   2. Go to Application → Local Storage")
    print("   3. Look for Supabase auth data")
    print("   4. Find the NEWEST access_token")
    print("   5. Copy the entire JWT token string")
    print()
    
    print("🔧 STEP 5: Verify Token Freshness")
    print("   1. The token should start with: eyJhbGciOiJIUzI1NiIs...")
    print("   2. It should be different from the previous token")
    print("   3. Check the timestamp in the token payload")
    print()

async def create_token_validator():
    """Create a simple token validator for quick testing."""
    print("\n🛠️  Quick Token Validator...")
    print("=" * 40)
    
    print("Paste your new JWT token here to validate it:")
    print("(Press Enter without input to skip)")
    
    try:
        token = input("JWT Token: ").strip()
        
        if not token:
            print("⏭️  Skipped token validation")
            return
        
        # Quick validation
        payload = jwt.decode(token, options={"verify_signature": False})
        
        import time
        current_time = int(time.time())
        token_exp = payload.get('exp', 0)
        token_iat = payload.get('iat', 0)
        
        print(f"\n📊 Token Analysis:")
        print(f"   Current Time: {current_time}")
        print(f"   Token Issued: {token_iat}")
        print(f"   Token Expires: {token_exp}")
        print(f"   Time to Expiry: {token_exp - current_time} seconds")
        
        if token_exp > current_time:
            print(f"   ✅ Token is VALID for {token_exp - current_time} more seconds")
            
            # Test with backend
            print(f"\n🧪 Testing with backend...")
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get("http://localhost:8000/api/moodboard/list", headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ Backend authentication SUCCESS!")
                print(f"   Response: {response.json()}")
            else:
                print(f"   ❌ Backend authentication failed: {response.status_code}")
                print(f"   Error: {response.text}")
        else:
            print(f"   ❌ Token is EXPIRED by {current_time - token_exp} seconds")
            
    except Exception as e:
        print(f"❌ Token validation failed: {e}")

async def check_frontend_auth_state():
    """Check if the frontend is properly managing auth state."""
    print("\n🔍 Frontend Authentication State Analysis...")
    print("=" * 50)
    
    print("🔧 Common Issues and Solutions:")
    print()
    
    print("1. 🐛 Token Not Refreshing:")
    print("   - Check if Supabase client is configured for auto-refresh")
    print("   - Verify refresh token is being stored and used")
    print("   - Look for token refresh errors in browser console")
    print()
    
    print("2. 🐛 Cached Token Issue:")
    print("   - Clear browser cache completely")
    print("   - Check if app is using stale localStorage data")
    print("   - Verify token is being updated on successful refresh")
    print()
    
    print("3. 🐛 Supabase Configuration:")
    print("   - Check JWT expiry settings in Supabase dashboard")
    print("   - Verify refresh token expiry settings")
    print("   - Check if auto-refresh is enabled in client config")
    print()
    
    print("4. 🐛 Session Management:")
    print("   - Check if session is being properly maintained")
    print("   - Verify onAuthStateChange listeners are working")
    print("   - Look for session persistence issues")

async def main():
    """Run diagnostic tests."""
    print("🔍 JWT Token Generation Issue Diagnostic")
    print("=" * 60)
    print("This tool will help identify why fresh tokens are not being generated.")
    print()
    
    # Test Supabase endpoints
    await test_supabase_auth_endpoints()
    
    # Generate instructions
    await generate_test_instructions()
    
    # Check frontend issues
    await check_frontend_auth_state()
    
    # Quick token validator
    await create_token_validator()
    
    print("\n🎯 Summary")
    print("=" * 30)
    print("The token generation issue is likely caused by:")
    print("1. Frontend not properly refreshing tokens")
    print("2. Cached authentication data in browser")
    print("3. Supabase session management configuration")
    print("4. Token refresh mechanism not working")
    print()
    print("Follow the steps above to get a truly fresh token and test again.")

if __name__ == "__main__":
    asyncio.run(main())
