#!/usr/bin/env python3
"""
Test script to verify JWT authentication integration between frontend and backend.
This script tests the authentication flow end-to-end.
"""

import asyncio
import logging
import sys
import os
import requests
import json
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token, get_current_user_from_token
from app.core.supabase import SUPABASE_URL

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Backend URL
BACKEND_URL = "http://localhost:8000"

async def test_jwks_endpoint():
    """Test that we can access Supabase JWKS endpoint."""
    print("\n🔍 Testing JWKS Endpoint Access...")
    print("=" * 50)

    try:
        jwks_url = f"{SUPABASE_URL}/auth/v1/jwks"

        # Try with anon key first (this is the correct approach)
        from app.core.supabase import SUPABASE_ANON_KEY
        headers = {"apikey": SUPABASE_ANON_KEY}
        response = requests.get(jwks_url, headers=headers, timeout=10)

        if response.status_code == 200:
            jwks = response.json()
            keys = jwks.get("keys", [])
            print(f"✅ JWKS endpoint accessible with anon key")
            print(f"✅ Found {len(keys)} signing keys")

            for i, key in enumerate(keys):
                kid = key.get("kid", "unknown")
                alg = key.get("alg", "unknown")
                print(f"   Key {i+1}: ID={kid}, Algorithm={alg}")

            return True
        else:
            print(f"❌ JWKS endpoint error: {response.status_code}")
            print(f"   Response: {response.text}")

            # Try without API key (some Supabase instances allow this)
            response_no_key = requests.get(jwks_url, timeout=10)
            if response_no_key.status_code == 200:
                print("✅ JWKS endpoint accessible without API key")
                return True

            return False

    except Exception as e:
        print(f"❌ JWKS endpoint error: {e}")
        return False

async def test_backend_health():
    """Test that backend is running and accessible."""
    print("\n🔍 Testing Backend Health...")
    print("=" * 50)

    try:
        # Test basic health endpoint (correct path)
        response = requests.get(f"{BACKEND_URL}/api/v1/health", timeout=10)

        if response.status_code == 200:
            print("✅ Backend health endpoint accessible")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

async def test_protected_endpoint_without_auth():
    """Test that protected endpoints properly reject unauthenticated requests."""
    print("\n🔍 Testing Protected Endpoint (No Auth)...")
    print("=" * 50)
    
    try:
        # Test a protected endpoint without authentication
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", timeout=10)
        
        if response.status_code == 401:
            print("✅ Protected endpoint correctly rejects unauthenticated requests")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Protected endpoint should return 401, got: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing protected endpoint: {e}")
        return False

async def test_protected_endpoint_with_invalid_auth():
    """Test that protected endpoints properly reject invalid tokens."""
    print("\n🔍 Testing Protected Endpoint (Invalid Auth)...")
    print("=" * 50)
    
    try:
        # Test with invalid token
        headers = {"Authorization": "Bearer invalid_token_here"}
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        if response.status_code == 401:
            print("✅ Protected endpoint correctly rejects invalid tokens")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Protected endpoint should return 401 for invalid token, got: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing invalid auth: {e}")
        return False

async def test_auth_header_formats():
    """Test various authorization header formats."""
    print("\n🔍 Testing Authorization Header Formats...")
    print("=" * 50)
    
    test_cases = [
        ("No header", {}),
        ("Missing Bearer", {"Authorization": "just_a_token"}),
        ("Empty Bearer", {"Authorization": "Bearer "}),
        ("Malformed", {"Authorization": "NotBearer token"}),
    ]
    
    for test_name, headers in test_cases:
        try:
            response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=5)
            
            if response.status_code == 401:
                print(f"✅ {test_name}: Correctly rejected (401)")
            else:
                print(f"❌ {test_name}: Expected 401, got {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_name}: Error - {e}")

async def get_frontend_instructions():
    """Provide instructions for getting a real JWT token from frontend."""
    print("\n📋 Instructions for Testing with Real JWT Token...")
    print("=" * 60)
    print("To test with a real JWT token from your frontend:")
    print()
    print("1. Open your browser and go to: http://localhost:3002")
    print("2. Log in with your credentials")
    print("3. Open browser Developer Tools (F12)")
    print("4. Go to Application tab → Local Storage")
    print("5. Look for Supabase auth data")
    print("6. Copy the 'access_token' value")
    print("7. Run this command to test:")
    print()
    print("   curl -X GET 'http://localhost:8000/api/moodboard/list' \\")
    print("     -H 'Authorization: Bearer YOUR_COPIED_TOKEN'")
    print()
    print("Expected result: Should return user's moodboards or empty array")

async def main():
    """Run all authentication integration tests."""
    print("🧪 JWT Authentication Integration Test Suite")
    print("=" * 60)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Supabase URL: {SUPABASE_URL}")
    print()
    
    # Run tests
    tests = [
        ("JWKS Endpoint", test_jwks_endpoint),
        ("Backend Health", test_backend_health),
        ("Protected Endpoint (No Auth)", test_protected_endpoint_without_auth),
        ("Protected Endpoint (Invalid Auth)", test_protected_endpoint_with_invalid_auth),
        ("Auth Header Formats", test_auth_header_formats),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Show summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    # Show next steps
    await get_frontend_instructions()
    
    if passed == len(results):
        print("\n🎉 All integration tests passed!")
        print("Your JWT authentication setup is working correctly.")
        print("The issue is likely in the frontend token handling.")
    else:
        print("\n🔧 Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
