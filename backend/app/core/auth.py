"""Authentication module for API endpoints with Supabase integration."""

import logging
import jwt
import requests
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends, Header
from app.core.supabase import supabase, SUPABASE_URL

logger = logging.getLogger(__name__)


async def verify_api_key():
    """
    Simple API key verification.
    For now, this is a placeholder that always passes.
    In production, you would implement proper API key validation.
    """
    # For development, we'll skip API key validation
    # In production, implement proper API key checking here
    return True


async def verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    Verify JWT token by decoding and validating with Supabase's public key.

    Args:
        token: JWT access token from Supabase client

    Returns:
        Decoded token payload with user information

    Raises:
        HTTPException: If token is invalid, expired, or malformed
    """
    try:
        # First, decode without verification to get the header
        unverified_header = jwt.get_unverified_header(token)

        # Try to get Supabase's public key for JWT verification
        jwks_url = f"{SUPABASE_URL}/auth/v1/jwks"

        # Try with anon key first
        from app.core.supabase import SUPABASE_ANON_KEY
        headers = {"apikey": SUPABASE_ANON_KEY}

        try:
            jwks_response = requests.get(jwks_url, headers=headers, timeout=10)
            jwks_response.raise_for_status()
            jwks = jwks_response.json()
        except requests.RequestException:
            # Fallback: try without API key
            try:
                jwks_response = requests.get(jwks_url, timeout=10)
                jwks_response.raise_for_status()
                jwks = jwks_response.json()
            except requests.RequestException as e:
                logger.error(f"Failed to fetch JWKS from {jwks_url}: {str(e)}")
                # TEMPORARY FALLBACK: Validate token structure without signature verification
                # This is for development/testing only - NOT for production
                logger.warning("⚠️  Using fallback token validation (development only)")
                return await _verify_token_fallback(token)

        # Find the correct key
        key = None
        for jwk in jwks.get("keys", []):
            if jwk.get("kid") == unverified_header.get("kid"):
                key = jwt.algorithms.RSAAlgorithm.from_jwk(jwk)
                break

        if not key:
            logger.error(f"Unable to find key with kid: {unverified_header.get('kid')}")
            logger.error(f"Available keys: {[k.get('kid') for k in jwks.get('keys', [])]}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Unable to find appropriate key for token verification"
            )

        # Verify and decode the token
        payload = jwt.decode(
            token,
            key,
            algorithms=["RS256"],
            audience="authenticated",
            issuer=f"{SUPABASE_URL}/auth/v1"
        )

        logger.info(f"✅ JWT token verified successfully for user: {payload.get('sub')}")
        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired. Please log in again."
        )
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token. Please log in again."
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


async def _verify_token_fallback(token: str) -> Dict[str, Any]:
    """
    Fallback token verification for development/testing when JWKS is not accessible.

    WARNING: This does NOT verify the token signature and should NEVER be used in production.
    This is only for development/testing purposes when JWKS endpoint is not accessible.
    """
    try:
        # Decode without signature verification (DEVELOPMENT ONLY)
        payload = jwt.decode(token, options={"verify_signature": False})

        # Basic validation
        required_fields = ["sub", "email", "iat", "exp"]
        for field in required_fields:
            if field not in payload:
                raise ValueError(f"Missing required field: {field}")

        # Check if token is expired
        import time
        current_time = int(time.time())
        exp_time = payload.get("exp", 0)

        # Debug logging for token expiration
        logger.info(f"Token expiration check: current={current_time}, exp={exp_time}, diff={exp_time - current_time}")

        if exp_time < current_time:
            raise ValueError(f"Token has expired. Current: {current_time}, Exp: {exp_time}")

        # Check issuer (basic validation)
        expected_issuer = f"{SUPABASE_URL}/auth/v1"
        if payload.get("iss") != expected_issuer:
            logger.warning(f"Issuer mismatch. Expected: {expected_issuer}, Got: {payload.get('iss')}")

        # Check audience
        if payload.get("aud") != "authenticated":
            logger.warning(f"Audience mismatch. Expected: authenticated, Got: {payload.get('aud')}")

        logger.warning(f"⚠️  Token validated using fallback method (NO signature verification) for user: {payload.get('sub')}")
        return payload

    except Exception as e:
        logger.error(f"Fallback token validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}"
        )


async def get_current_user_from_token(authorization: Optional[str] = Header(None)) -> Dict[str, Any]:
    """
    Get current user information from Supabase JWT token using proper JWT verification.

    Args:
        authorization: Authorization header with Bearer token

    Returns:
        User information dictionary

    Raises:
        HTTPException: If token is invalid or user not found
    """
    # Require authentication for mood board operations
    if not authorization:
        logger.warning("No authorization header provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header required. Please log in to access this resource."
        )

    try:
        # Extract token from "Bearer <token>" format
        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header format. Expected 'Bearer <token>'"
            )

        token = authorization.split(" ")[1]

        # Verify token using proper JWT verification
        payload = await verify_jwt_token(token)

        # Extract user information from JWT payload
        user_id = payload.get("sub")
        email = payload.get("email")
        user_metadata = payload.get("user_metadata", {})

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload. Missing user information."
            )

        return {
            "user_id": user_id,
            "username": user_metadata.get("full_name") or email.split("@")[0],
            "email": email,
            "metadata": user_metadata,
            "jwt_token": token  # ✅ Include the raw JWT token for storage operations
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying user token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}"
        )


def get_current_user():
    """
    Get current user information.
    Placeholder for user authentication.
    """
    return {"user_id": "anonymous", "username": "anonymous"}


def require_auth():
    """
    Dependency that requires authentication.
    Placeholder for authentication requirement.
    """
    return True


# Dependency for getting current user in endpoints
CurrentUser = Depends(get_current_user_from_token)
