#!/usr/bin/env python3
"""
Test the real JWT token from the frontend directly.
"""

import asyncio
import logging
import sys
import os
import requests
import json
import jwt

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token, _verify_token_fallback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The fresh JWT token from the frontend (updated)
REAL_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsImtpZCI6ImxYTDdOTStrSkVJWWJPak0iLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4ISm4BcDTaHEHHHfXeDpVJccmkDs6KdxxZkIwi4uGF0"

# Session information provided
EXPECTED_EXPIRES_AT = 1754258549  # Unix timestamp from session data
EXPECTED_EXPIRES_IN = 3600  # 1 hour in seconds

BACKEND_URL = "http://localhost:8000"

async def test_time_analysis():
    """Analyze time-related issues with token generation and validation."""
    print("\n🔍 Time Analysis - Investigating Token Generation Issues...")
    print("=" * 70)

    import time
    from datetime import datetime, timezone

    try:
        # Get current time in different formats
        current_unix = int(time.time())
        current_dt = datetime.now(timezone.utc)

        # Decode token to get timestamps
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})
        token_exp = payload.get('exp', 0)
        token_iat = payload.get('iat', 0)

        print(f"🕐 Current System Time:")
        print(f"   Unix Timestamp: {current_unix}")
        print(f"   UTC DateTime: {current_dt}")
        print(f"   Local DateTime: {datetime.now()}")

        print(f"\n🎫 Token Timestamps:")
        print(f"   Issued At (iat): {token_iat}")
        print(f"   Expires At (exp): {token_exp}")
        print(f"   Expected Expires: {EXPECTED_EXPIRES_AT}")

        print(f"\n📊 Time Differences:")
        time_diff = current_unix - token_exp
        iat_diff = current_unix - token_iat
        expected_diff = current_unix - EXPECTED_EXPIRES_AT

        print(f"   Current - Token Exp: {time_diff} seconds ({time_diff/3600:.2f} hours)")
        print(f"   Current - Token Iat: {iat_diff} seconds ({iat_diff/3600:.2f} hours)")
        print(f"   Current - Expected Exp: {expected_diff} seconds ({expected_diff/3600:.2f} hours)")

        print(f"\n🔍 Analysis:")
        if time_diff > 0:
            print(f"   ❌ Token is EXPIRED by {time_diff} seconds ({time_diff/3600:.2f} hours)")
        else:
            print(f"   ✅ Token is VALID for {abs(time_diff)} more seconds ({abs(time_diff)/3600:.2f} hours)")

        if token_exp == EXPECTED_EXPIRES_AT:
            print(f"   ✅ Token expiration matches session data")
        else:
            print(f"   ⚠️  Token expiration differs from session data")

        # Check if this looks like a timezone issue
        timezone_offsets = [-12, -8, -5, 0, 3, 8, 12]  # Common timezone offsets
        print(f"\n🌍 Timezone Analysis:")
        for offset in timezone_offsets:
            adjusted_time = current_unix + (offset * 3600)
            diff_with_offset = adjusted_time - token_exp
            if abs(diff_with_offset) < 300:  # Within 5 minutes
                print(f"   🎯 UTC{offset:+d}: Would be valid (diff: {diff_with_offset}s)")

        return time_diff <= 0  # Return True if token is not expired

    except Exception as e:
        print(f"❌ Time analysis failed: {e}")
        return False

async def test_token_structure():
    """Test the token structure without verification."""
    print("\n🔍 Testing Token Structure...")
    print("=" * 50)

    try:
        # Decode without verification
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})

        print("✅ Token decoded successfully!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Full Name: {payload.get('user_metadata', {}).get('full_name')}")
        print(f"   Username: {payload.get('user_metadata', {}).get('username')}")
        print(f"   Issuer: {payload.get('iss')}")
        print(f"   Audience: {payload.get('aud')}")
        print(f"   Expires: {payload.get('exp')}")
        print(f"   Issued: {payload.get('iat')}")
        print(f"   Key ID: {jwt.get_unverified_header(REAL_JWT_TOKEN).get('kid')}")

        return True

    except Exception as e:
        print(f"❌ Token structure test failed: {e}")
        return False

async def test_fallback_verification():
    """Test the fallback verification method."""
    print("\n🔍 Testing Fallback Verification...")
    print("=" * 50)
    
    try:
        payload = await _verify_token_fallback(REAL_JWT_TOKEN)
        print("✅ Fallback verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Fallback verification failed: {e}")
        return False

async def test_full_verification():
    """Test the full JWT verification method."""
    print("\n🔍 Testing Full JWT Verification...")
    print("=" * 50)
    
    try:
        payload = await verify_jwt_token(REAL_JWT_TOKEN)
        print("✅ Full JWT verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Full JWT verification failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

async def test_backend_endpoint():
    """Test the backend endpoint with the real token."""
    print("\n🔍 Testing Backend Endpoint...")
    print("=" * 50)
    
    try:
        headers = {
            "Authorization": f"Bearer {REAL_JWT_TOKEN}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend endpoint successful!")
            print(f"   Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Backend endpoint failed")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend endpoint test failed: {e}")
        return False

async def test_other_endpoints():
    """Test other protected endpoints."""
    print("\n🔍 Testing Other Protected Endpoints...")
    print("=" * 50)
    
    endpoints = [
        "/api/focus-group/recent",
        "/api/palettes",
        "/api/v1/health"  # This should work without auth
    ]
    
    headers = {
        "Authorization": f"Bearer {REAL_JWT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    results = []
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
            status = "✅ SUCCESS" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"   {endpoint}: {status}")
            results.append(response.status_code == 200)
            
        except Exception as e:
            print(f"   {endpoint}: ❌ ERROR - {e}")
            results.append(False)
    
    return any(results)

async def main():
    """Run all tests."""
    print("🧪 Real JWT Token Authentication Test")
    print("=" * 60)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Token Length: {len(REAL_JWT_TOKEN)} characters")
    print()
    
    # Run tests
    tests = [
        ("Time Analysis", test_time_analysis),
        ("Token Structure", test_token_structure),
        ("Fallback Verification", test_fallback_verification),
        ("Full JWT Verification", test_full_verification),
        ("Backend Endpoint", test_backend_endpoint),
        ("Other Endpoints", test_other_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Show summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    # Conclusions
    print("\n🎯 Conclusions")
    print("=" * 30)
    
    if passed >= 3:
        print("✅ JWT authentication is working!")
        print("✅ The token is valid and backend accepts it")
        print("✅ Integration between frontend and backend is functional")
    elif passed >= 1:
        print("🔧 Partial success - some components working")
        print("🔍 Check failed tests for specific issues")
    else:
        print("❌ Authentication integration has significant issues")
        print("🔧 Review token format and backend configuration")

if __name__ == "__main__":
    asyncio.run(main())
