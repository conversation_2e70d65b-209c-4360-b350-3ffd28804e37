#!/usr/bin/env python3
"""
Test the real JWT token from the frontend directly.
"""

import asyncio
import logging
import sys
import os
import requests
import json
import jwt

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token, _verify_token_fallback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The real JWT token from the frontend
REAL_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsImtpZCI6ImxYTDdOTStrSkVJWWJPak0iLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4ISm4BcDTaHEHHHfXeDpVJccmkDs6KdxxZkIwi4uGF0"

BACKEND_URL = "http://localhost:8000"

async def test_token_structure():
    """Test the token structure without verification."""
    print("\n🔍 Testing Token Structure...")
    print("=" * 50)
    
    try:
        # Decode without verification
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})
        
        print("✅ Token decoded successfully!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Full Name: {payload.get('user_metadata', {}).get('full_name')}")
        print(f"   Username: {payload.get('user_metadata', {}).get('username')}")
        print(f"   Issuer: {payload.get('iss')}")
        print(f"   Audience: {payload.get('aud')}")
        print(f"   Expires: {payload.get('exp')}")
        print(f"   Key ID: {jwt.get_unverified_header(REAL_JWT_TOKEN).get('kid')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token structure test failed: {e}")
        return False

async def test_fallback_verification():
    """Test the fallback verification method."""
    print("\n🔍 Testing Fallback Verification...")
    print("=" * 50)
    
    try:
        payload = await _verify_token_fallback(REAL_JWT_TOKEN)
        print("✅ Fallback verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Fallback verification failed: {e}")
        return False

async def test_full_verification():
    """Test the full JWT verification method."""
    print("\n🔍 Testing Full JWT Verification...")
    print("=" * 50)
    
    try:
        payload = await verify_jwt_token(REAL_JWT_TOKEN)
        print("✅ Full JWT verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Full JWT verification failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

async def test_backend_endpoint():
    """Test the backend endpoint with the real token."""
    print("\n🔍 Testing Backend Endpoint...")
    print("=" * 50)
    
    try:
        headers = {
            "Authorization": f"Bearer {REAL_JWT_TOKEN}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend endpoint successful!")
            print(f"   Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Backend endpoint failed")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend endpoint test failed: {e}")
        return False

async def test_other_endpoints():
    """Test other protected endpoints."""
    print("\n🔍 Testing Other Protected Endpoints...")
    print("=" * 50)
    
    endpoints = [
        "/api/focus-group/recent",
        "/api/palettes",
        "/api/v1/health"  # This should work without auth
    ]
    
    headers = {
        "Authorization": f"Bearer {REAL_JWT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    results = []
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
            status = "✅ SUCCESS" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"   {endpoint}: {status}")
            results.append(response.status_code == 200)
            
        except Exception as e:
            print(f"   {endpoint}: ❌ ERROR - {e}")
            results.append(False)
    
    return any(results)

async def main():
    """Run all tests."""
    print("🧪 Real JWT Token Authentication Test")
    print("=" * 60)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Token Length: {len(REAL_JWT_TOKEN)} characters")
    print()
    
    # Run tests
    tests = [
        ("Token Structure", test_token_structure),
        ("Fallback Verification", test_fallback_verification),
        ("Full JWT Verification", test_full_verification),
        ("Backend Endpoint", test_backend_endpoint),
        ("Other Endpoints", test_other_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Show summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    # Conclusions
    print("\n🎯 Conclusions")
    print("=" * 30)
    
    if passed >= 3:
        print("✅ JWT authentication is working!")
        print("✅ The token is valid and backend accepts it")
        print("✅ Integration between frontend and backend is functional")
    elif passed >= 1:
        print("🔧 Partial success - some components working")
        print("🔍 Check failed tests for specific issues")
    else:
        print("❌ Authentication integration has significant issues")
        print("🔧 Review token format and backend configuration")

if __name__ == "__main__":
    asyncio.run(main())
