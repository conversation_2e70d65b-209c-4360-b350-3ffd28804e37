#!/usr/bin/env python3
"""
Test the real JWT token from the frontend directly.
"""

import asyncio
import logging
import sys
import os
import requests
import json
import jwt

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token, _verify_token_fallback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# JWT token from authentication session data (Usuario Demo issue investigation)
REAL_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsImtpZCI6ImxYTDdOTStrSkVJWWJPak0iLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4ISm4BcDTaHEHHHfXeDpVJccmkDs6KdxxZkIwi4uGF0"

# Session information from frontend (Usuario Demo issue)
EXPECTED_EXPIRES_AT = 1754258549  # Unix timestamp from session data
EXPECTED_EXPIRES_IN = 3600  # 1 hour in seconds
REFRESH_TOKEN = "c7kxy2fzk7wj"
USER_ID = "8142a43b-add3-46ce-9eda-2d9b1dc81f56"
SESSION_HASH = "051f2c24caa8709f968bfc030137daa1d999df3e3f00f9742d36e8aca2289276719c4a090d4c529ce76988ad3af9f0cbd28c8c2f25ca171f"

# Expected user information that should be displayed instead of "Usuario Demo"
EXPECTED_USER_NAME = "Alejandro Acevedo Granados"
EXPECTED_USERNAME = "Alekei"
EXPECTED_EMAIL = "<EMAIL>"

BACKEND_URL = "http://localhost:8000"

async def test_time_analysis():
    """Analyze time-related issues with token generation and validation."""
    print("\n🔍 Time Analysis - Investigating Token Generation Issues...")
    print("=" * 70)

    import time
    from datetime import datetime, timezone

    try:
        # Get current time in different formats
        current_unix = int(time.time())
        current_dt = datetime.now(timezone.utc)

        # Decode token to get timestamps
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})
        token_exp = payload.get('exp', 0)
        token_iat = payload.get('iat', 0)

        print(f"🕐 Current System Time:")
        print(f"   Unix Timestamp: {current_unix}")
        print(f"   UTC DateTime: {current_dt}")
        print(f"   Local DateTime: {datetime.now()}")

        print(f"\n🎫 Token Timestamps:")
        print(f"   Issued At (iat): {token_iat}")
        print(f"   Expires At (exp): {token_exp}")
        print(f"   Expected Expires: {EXPECTED_EXPIRES_AT}")

        print(f"\n📊 Time Differences:")
        time_diff = current_unix - token_exp
        iat_diff = current_unix - token_iat
        expected_diff = current_unix - EXPECTED_EXPIRES_AT

        print(f"   Current - Token Exp: {time_diff} seconds ({time_diff/3600:.2f} hours)")
        print(f"   Current - Token Iat: {iat_diff} seconds ({iat_diff/3600:.2f} hours)")
        print(f"   Current - Expected Exp: {expected_diff} seconds ({expected_diff/3600:.2f} hours)")

        print(f"\n🔍 Analysis:")
        if time_diff > 0:
            print(f"   ❌ Token is EXPIRED by {time_diff} seconds ({time_diff/3600:.2f} hours)")
        else:
            print(f"   ✅ Token is VALID for {abs(time_diff)} more seconds ({abs(time_diff)/3600:.2f} hours)")

        if token_exp == EXPECTED_EXPIRES_AT:
            print(f"   ✅ Token expiration matches session data")
        else:
            print(f"   ⚠️  Token expiration differs from session data")

        # Check if this looks like a timezone issue
        timezone_offsets = [-12, -8, -5, 0, 3, 8, 12]  # Common timezone offsets
        print(f"\n🌍 Timezone Analysis:")
        for offset in timezone_offsets:
            adjusted_time = current_unix + (offset * 3600)
            diff_with_offset = adjusted_time - token_exp
            if abs(diff_with_offset) < 300:  # Within 5 minutes
                print(f"   🎯 UTC{offset:+d}: Would be valid (diff: {diff_with_offset}s)")

        return time_diff <= 0  # Return True if token is not expired

    except Exception as e:
        print(f"❌ Time analysis failed: {e}")
        return False

async def test_token_structure():
    """Test the token structure without verification."""
    print("\n🔍 Testing Token Structure...")
    print("=" * 50)

    try:
        # Decode without verification
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})

        print("✅ Token decoded successfully!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Full Name: {payload.get('user_metadata', {}).get('full_name')}")
        print(f"   Username: {payload.get('user_metadata', {}).get('username')}")
        print(f"   Issuer: {payload.get('iss')}")
        print(f"   Audience: {payload.get('aud')}")
        print(f"   Expires: {payload.get('exp')}")
        print(f"   Issued: {payload.get('iat')}")
        print(f"   Key ID: {jwt.get_unverified_header(REAL_JWT_TOKEN).get('kid')}")

        return True

    except Exception as e:
        print(f"❌ Token structure test failed: {e}")
        return False

async def test_fallback_verification():
    """Test the fallback verification method."""
    print("\n🔍 Testing Fallback Verification...")
    print("=" * 50)
    
    try:
        payload = await _verify_token_fallback(REAL_JWT_TOKEN)
        print("✅ Fallback verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Fallback verification failed: {e}")
        return False

async def test_full_verification():
    """Test the full JWT verification method."""
    print("\n🔍 Testing Full JWT Verification...")
    print("=" * 50)
    
    try:
        payload = await verify_jwt_token(REAL_JWT_TOKEN)
        print("✅ Full JWT verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        return True
        
    except Exception as e:
        print(f"❌ Full JWT verification failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

async def test_backend_endpoint():
    """Test the backend endpoint with the real token."""
    print("\n🔍 Testing Backend Endpoint...")
    print("=" * 50)
    
    try:
        headers = {
            "Authorization": f"Bearer {REAL_JWT_TOKEN}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend endpoint successful!")
            print(f"   Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Backend endpoint failed")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend endpoint test failed: {e}")
        return False

async def test_user_data_extraction():
    """Test if user data can be properly extracted from the JWT token."""
    print("\n🔍 Testing User Data Extraction (Usuario Demo Issue)...")
    print("=" * 60)

    try:
        # Decode token to extract user information
        payload = jwt.decode(REAL_JWT_TOKEN, options={"verify_signature": False})

        print("📋 User Information from JWT Token:")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Full Name: {payload.get('user_metadata', {}).get('full_name')}")
        print(f"   Username: {payload.get('user_metadata', {}).get('username')}")
        print(f"   Avatar URL: {payload.get('user_metadata', {}).get('avatar_url', 'Not provided')}")
        print(f"   Provider: {payload.get('app_metadata', {}).get('provider')}")
        print(f"   Role: {payload.get('role')}")

        print("\n🎯 Expected vs Actual:")
        actual_name = payload.get('user_metadata', {}).get('full_name')
        actual_username = payload.get('user_metadata', {}).get('username')
        actual_email = payload.get('email')
        actual_user_id = payload.get('sub')

        print(f"   Name: Expected='{EXPECTED_USER_NAME}', Actual='{actual_name}' {'✅' if actual_name == EXPECTED_USER_NAME else '❌'}")
        print(f"   Username: Expected='{EXPECTED_USERNAME}', Actual='{actual_username}' {'✅' if actual_username == EXPECTED_USERNAME else '❌'}")
        print(f"   Email: Expected='{EXPECTED_EMAIL}', Actual='{actual_email}' {'✅' if actual_email == EXPECTED_EMAIL else '❌'}")
        print(f"   User ID: Expected='{USER_ID}', Actual='{actual_user_id}' {'✅' if actual_user_id == USER_ID else '❌'}")

        # Check if all expected data is present
        has_all_data = all([
            actual_name == EXPECTED_USER_NAME,
            actual_username == EXPECTED_USERNAME,
            actual_email == EXPECTED_EMAIL,
            actual_user_id == USER_ID
        ])

        if has_all_data:
            print("\n✅ All user data is correctly present in the JWT token!")
            print("   The issue is likely in frontend state management or backend communication.")
        else:
            print("\n❌ Some user data is missing or incorrect in the JWT token.")
            print("   This could explain why 'Usuario Demo' is being displayed.")

        return has_all_data

    except Exception as e:
        print(f"❌ User data extraction failed: {e}")
        return False

async def test_frontend_backend_communication():
    """Test the communication flow that should update frontend user state."""
    print("\n🔍 Testing Frontend-Backend Communication Flow...")
    print("=" * 60)

    try:
        headers = {
            "Authorization": f"Bearer {REAL_JWT_TOKEN}",
            "Content-Type": "application/json"
        }

        # Test endpoints that might be used to get user information
        user_endpoints = [
            ("/api/moodboard/list", "Moodboard List"),
            ("/api/focus-group/recent", "Focus Group Recent"),
            ("/api/palettes", "Palettes"),
        ]

        print("📡 Testing endpoints that should return user-specific data:")

        successful_requests = 0
        for endpoint, description in user_endpoints:
            try:
                response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ {description}: SUCCESS")
                    print(f"      Response type: {type(data)}")
                    if isinstance(data, list):
                        print(f"      Items count: {len(data)}")
                    elif isinstance(data, dict):
                        print(f"      Keys: {list(data.keys())}")
                    successful_requests += 1
                else:
                    print(f"   ❌ {description}: {response.status_code}")
                    print(f"      Error: {response.text[:100]}...")

            except Exception as e:
                print(f"   ❌ {description}: ERROR - {e}")

        print(f"\n📊 Communication Results:")
        print(f"   Successful requests: {successful_requests}/{len(user_endpoints)}")

        if successful_requests > 0:
            print("   ✅ Backend is responding to authenticated requests")
            print("   🔍 Issue is likely in frontend state management")
        else:
            print("   ❌ Backend is not accepting the authentication token")
            print("   🔍 Issue is in token validation or backend authentication")

        return successful_requests > 0

    except Exception as e:
        print(f"❌ Communication test failed: {e}")
        return False

async def test_other_endpoints():
    """Test other protected endpoints."""
    print("\n🔍 Testing Other Protected Endpoints...")
    print("=" * 50)

    endpoints = [
        "/api/focus-group/recent",
        "/api/palettes",
        "/api/v1/health"  # This should work without auth
    ]

    headers = {
        "Authorization": f"Bearer {REAL_JWT_TOKEN}",
        "Content-Type": "application/json"
    }

    results = []

    for endpoint in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
            status = "✅ SUCCESS" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"   {endpoint}: {status}")
            results.append(response.status_code == 200)

        except Exception as e:
            print(f"   {endpoint}: ❌ ERROR - {e}")
            results.append(False)

    return any(results)

async def main():
    """Run all tests."""
    print("🧪 Real JWT Token Authentication Test")
    print("=" * 60)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Token Length: {len(REAL_JWT_TOKEN)} characters")
    print()
    
    # Run tests
    tests = [
        ("Time Analysis", test_time_analysis),
        ("Token Structure", test_token_structure),
        ("User Data Extraction", test_user_data_extraction),
        ("Fallback Verification", test_fallback_verification),
        ("Full JWT Verification", test_full_verification),
        ("Backend Endpoint", test_backend_endpoint),
        ("Frontend-Backend Communication", test_frontend_backend_communication),
        ("Other Endpoints", test_other_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Show summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    # Conclusions
    print("\n🎯 Conclusions - Usuario Demo Issue Analysis")
    print("=" * 50)

    if passed >= 5:
        print("✅ JWT authentication is working correctly!")
        print("✅ User data is properly encoded in the token")
        print("✅ Backend can extract user information successfully")
        print()
        print("🔍 Since authentication works but frontend shows 'Usuario Demo':")
        print("   → Issue is in FRONTEND STATE MANAGEMENT")
        print("   → Check React/Vue state updates after authentication")
        print("   → Verify onAuthStateChange listeners are working")
        print("   → Check if user context/store is being updated")
    elif passed >= 3:
        print("🔧 Partial authentication success")
        print("✅ Token structure and user data are correct")
        print()
        print("🔍 Possible causes for 'Usuario Demo' display:")
        print("   → Token validation issues in backend")
        print("   → Frontend not receiving user data from backend")
        print("   → Authentication state not persisting in frontend")
    elif passed >= 1:
        print("🔧 Limited authentication functionality")
        print("🔍 Check failed tests for specific issues")
        print("   → Token may be expired or invalid")
        print("   → Backend authentication logic needs review")
    else:
        print("❌ Authentication integration has significant issues")
        print("🔧 Review token format and backend configuration")
        print("   → Token generation or validation is failing")
        print("   → Backend authentication system needs debugging")

if __name__ == "__main__":
    asyncio.run(main())
