"""Add ProjectContent table for project content management

Revision ID: add_project_content
Revises: previous_revision
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_project_content'
down_revision = 'add_seo_gpt_optimizer_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create project_contents table
    op.create_table('project_contents',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('content_id', sa.String(length=64), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('content_type', sa.String(length=50), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('content_text', sa.Text(), nullable=True),
        sa.Column('content_html', sa.Text(), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('content_length', sa.Integer(), nullable=True),
        sa.Column('current_gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('target_gpt_rank_score', sa.Float(), nullable=True),
        sa.Column('seo_score', sa.Float(), nullable=True),
        sa.Column('target_keywords', sa.Text(), nullable=True),
        sa.Column('meta_description', sa.String(length=160), nullable=True),
        sa.Column('slug', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('published_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_id'], ['seo_gpt_projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('idx_project_contents_project_type', 'project_contents', ['project_id', 'content_type'])
    op.create_index('idx_project_contents_status_created', 'project_contents', ['status', 'created_at'])
    op.create_index(op.f('ix_project_contents_content_id'), 'project_contents', ['content_id'], unique=True)
    op.create_index(op.f('ix_project_contents_content_type'), 'project_contents', ['content_type'])
    op.create_index(op.f('ix_project_contents_created_at'), 'project_contents', ['created_at'])
    op.create_index(op.f('ix_project_contents_id'), 'project_contents', ['id'])
    op.create_index(op.f('ix_project_contents_project_id'), 'project_contents', ['project_id'])
    op.create_index(op.f('ix_project_contents_status'), 'project_contents', ['status'])


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_project_contents_status'), table_name='project_contents')
    op.drop_index(op.f('ix_project_contents_project_id'), table_name='project_contents')
    op.drop_index(op.f('ix_project_contents_id'), table_name='project_contents')
    op.drop_index(op.f('ix_project_contents_created_at'), table_name='project_contents')
    op.drop_index(op.f('ix_project_contents_content_type'), table_name='project_contents')
    op.drop_index(op.f('ix_project_contents_content_id'), table_name='project_contents')
    op.drop_index('idx_project_contents_status_created', table_name='project_contents')
    op.drop_index('idx_project_contents_project_type', table_name='project_contents')
    
    # Drop table
    op.drop_table('project_contents')
