#!/usr/bin/env python3
"""
Test script for Project Content Management API
Tests the new project content endpoints to ensure they work correctly.
"""

import requests
import json
import uuid
from datetime import datetime

# Configuration
API_BASE = "http://localhost:8001/api/v1/seo-gpt"
HEADERS = {"Content-Type": "application/json"}

def test_project_content_management():
    """Test the complete project content management workflow"""
    
    print("🧪 Testing Project Content Management API")
    print("=" * 50)
    
    # Step 1: Create a test project
    print("\n1. Creating test project...")
    project_data = {
        "title": "Test Project for Content Management",
        "topic": "test content management",
        "target_language": "es",
        "content_type": "ARTICLE",
        "target_gpt_rank_score": 95.0
    }
    
    response = requests.post(f"{API_BASE}/projects/create", json=project_data, headers=HEADERS)
    if response.status_code == 201:
        project_result = response.json()
        project_id = project_result["project_id"]
        print(f"✅ Project created: {project_id}")
    else:
        print(f"❌ Failed to create project: {response.status_code}")
        print(response.text)
        return
    
    # Step 2: Create content in the project
    print("\n2. Creating content in project...")
    content_data = {
        "title": "Test Blog Post",
        "content_type": "blog",
        "content_text": "This is a test blog post content for testing the project content management system.",
        "target_keywords": ["test", "content management", "SEO"],
        "meta_description": "Test blog post for content management testing"
    }
    
    response = requests.post(f"{API_BASE}/projects/{project_id}/contents", json=content_data, headers=HEADERS)
    if response.status_code == 201:
        content_result = response.json()
        content_id = content_result["data"]["content_id"]
        print(f"✅ Content created: {content_id}")
    else:
        print(f"❌ Failed to create content: {response.status_code}")
        print(response.text)
        return
    
    # Step 3: Get project contents
    print("\n3. Getting project contents...")
    response = requests.get(f"{API_BASE}/projects/{project_id}/contents", headers=HEADERS)
    if response.status_code == 200:
        contents_result = response.json()
        contents = contents_result["data"]["contents"]
        print(f"✅ Retrieved {len(contents)} content items")
        for content in contents:
            print(f"   - {content['title']} ({content['content_type']}) - {content['word_count']} words")
    else:
        print(f"❌ Failed to get contents: {response.status_code}")
        print(response.text)
    
    # Step 4: Get specific content
    print("\n4. Getting specific content...")
    response = requests.get(f"{API_BASE}/projects/{project_id}/contents/{content_id}", headers=HEADERS)
    if response.status_code == 200:
        content_detail = response.json()
        print(f"✅ Retrieved content: {content_detail['data']['title']}")
        print(f"   Content length: {content_detail['data']['content_length']} chars")
        print(f"   Word count: {content_detail['data']['word_count']} words")
    else:
        print(f"❌ Failed to get content: {response.status_code}")
        print(response.text)
    
    # Step 5: Update content
    print("\n5. Updating content...")
    update_data = {
        "content_text": "This is updated content for testing the project content management system. It has more words now.",
        "status": "published",
        "current_gpt_rank_score": 85.5
    }
    
    response = requests.patch(f"{API_BASE}/projects/{project_id}/contents/{content_id}", json=update_data, headers=HEADERS)
    if response.status_code == 200:
        update_result = response.json()
        print(f"✅ Content updated successfully")
        print(f"   New word count: {update_result['data']['word_count']}")
        print(f"   Status: {update_result['data']['status']}")
    else:
        print(f"❌ Failed to update content: {response.status_code}")
        print(response.text)
    
    # Step 6: Test research with project linking
    print("\n6. Testing research with project linking...")
    research_data = {
        "topic": "test research topic",
        "target_language": "es",
        "project_id": project_id,
        "include_reddit": True,
        "include_quora": True
    }
    
    response = requests.post(f"{API_BASE}/research", json=research_data, headers=HEADERS)
    if response.status_code == 200:
        research_result = response.json()
        print(f"✅ Research completed and linked to project")
        print(f"   Topic: {research_result['data']['topic']}")
        print(f"   Confidence: {research_result['data']['research_summary']['research_confidence']:.2f}")
    else:
        print(f"❌ Failed to conduct research: {response.status_code}")
        print(response.text)
    
    # Step 7: Get project research
    print("\n7. Getting project research...")
    response = requests.get(f"{API_BASE}/projects/{project_id}/research", headers=HEADERS)
    if response.status_code == 200:
        research_result = response.json()
        research_items = research_result["data"]["research"]
        print(f"✅ Retrieved {len(research_items)} research items")
        for research in research_items:
            print(f"   - {research['topic']} (confidence: {research['research_confidence']:.2f})")
    else:
        print(f"❌ Failed to get project research: {response.status_code}")
        print(response.text)
    
    # Step 8: Clean up - Delete content and project
    print("\n8. Cleaning up...")
    
    # Delete content
    response = requests.delete(f"{API_BASE}/projects/{project_id}/contents/{content_id}", headers=HEADERS)
    if response.status_code == 200:
        print("✅ Content deleted successfully")
    else:
        print(f"❌ Failed to delete content: {response.status_code}")
    
    # Delete project
    response = requests.delete(f"{API_BASE}/projects/{project_id}", headers=HEADERS)
    if response.status_code == 200:
        print("✅ Project deleted successfully")
    else:
        print(f"❌ Failed to delete project: {response.status_code}")
    
    print("\n🎉 Project Content Management API test completed!")

if __name__ == "__main__":
    test_project_content_management()
