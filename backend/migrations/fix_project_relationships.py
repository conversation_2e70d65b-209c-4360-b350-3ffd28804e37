"""
Database migration to fix project-to-content relationships
Converts foreign key references from integer IDs to string UUIDs
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.db.seo_gpt_models import SEOGPTProject, ProjectContent, ContentAnalysis, GPTRankHistory, KeywordResearch

def run_migration():
    """Run the database migration to fix project relationships."""

    # Create database connection
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        print("🔧 Starting database migration to fix project relationships...")
        print("⚠️ Using SQLite-compatible approach...")

        # For SQLite, we need to recreate tables instead of altering them
        # Step 1: Create new tables with correct foreign keys

        print("📝 Creating new project_contents table...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS project_contents_new (
                id INTEGER PRIMARY KEY,
                content_id VARCHAR(64) UNIQUE NOT NULL,
                project_id VARCHAR(64) NOT NULL,
                title VARCHAR(255) NOT NULL,
                content_type VARCHAR(50) DEFAULT 'blog',
                status VARCHAR(20) DEFAULT 'draft',
                content_text TEXT,
                content_html TEXT,
                word_count INTEGER DEFAULT 0,
                content_length INTEGER DEFAULT 0,
                current_gpt_rank_score FLOAT DEFAULT 0.0,
                target_gpt_rank_score FLOAT DEFAULT 95.0,
                seo_score FLOAT DEFAULT 0.0,
                target_keywords TEXT,
                meta_description VARCHAR(160),
                slug VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                published_at DATETIME,
                FOREIGN KEY (project_id) REFERENCES seo_gpt_projects(project_id)
            )
        """))

        # Copy data from old table to new table
        print("📝 Copying project content data...")
        db.execute(text("""
            INSERT INTO project_contents_new (
                id, content_id, project_id, title, content_type, status,
                content_text, content_html, word_count, content_length,
                current_gpt_rank_score, target_gpt_rank_score, seo_score,
                target_keywords, meta_description, slug, created_at, updated_at, published_at
            )
            SELECT
                pc.id, pc.content_id, sp.project_id, pc.title, pc.content_type, pc.status,
                pc.content_text, pc.content_html, pc.word_count, pc.content_length,
                pc.current_gpt_rank_score, pc.target_gpt_rank_score, pc.seo_score,
                pc.target_keywords, pc.meta_description, pc.slug, pc.created_at, pc.updated_at, pc.published_at
            FROM project_contents pc
            JOIN seo_gpt_projects sp ON pc.project_id = sp.id
        """))

        # Drop old table and rename new one
        db.execute(text("DROP TABLE project_contents"))
        db.execute(text("ALTER TABLE project_contents_new RENAME TO project_contents"))

        db.commit()
        print("✅ Updated ProjectContent table structure")
        
        # Step 2: Update ContentAnalysis table (if it has data)
        print("📝 Checking ContentAnalysis table...")

        analyses_count = db.execute(text("SELECT COUNT(*) FROM content_analyses")).scalar()
        print(f"Found {analyses_count} analysis records")

        if analyses_count > 0:
            print("📝 Updating ContentAnalysis table...")
            # Create new table structure
            db.execute(text("""
                CREATE TABLE content_analyses_new AS
                SELECT
                    ca.id, ca.analysis_id, sp.project_id, ca.content_version, ca.analysis_type,
                    ca.semantic_similarity_score, ca.logical_coherence_score, ca.authority_signals_score,
                    ca.citability_score, ca.clarity_score, ca.completeness_score, ca.gpt_rank_score,
                    ca.confidence_level, ca.score_grade, ca.word_count, ca.content_length,
                    ca.created_at, ca.updated_at
                FROM content_analyses ca
                JOIN seo_gpt_projects sp ON ca.project_id = sp.id
            """))

            db.execute(text("DROP TABLE content_analyses"))
            db.execute(text("ALTER TABLE content_analyses_new RENAME TO content_analyses"))
            print("✅ Updated ContentAnalysis table structure")
        else:
            print("⚠️ No ContentAnalysis records to migrate")
        
        # Step 3: Update other tables (simplified approach)
        print("📝 Checking other tables...")

        # Check GPTRankHistory
        history_count = db.execute(text("SELECT COUNT(*) FROM gpt_rank_history")).scalar()
        print(f"Found {history_count} history records")

        if history_count > 0:
            print("📝 Updating GPTRankHistory table...")
            db.execute(text("""
                CREATE TABLE gpt_rank_history_new AS
                SELECT
                    grh.id, sp.project_id, grh.gpt_rank_score, grh.score_change, grh.score_grade,
                    grh.semantic_similarity, grh.logical_coherence, grh.authority_signals,
                    grh.citability, grh.clarity, grh.completeness, grh.created_at
                FROM gpt_rank_history grh
                JOIN seo_gpt_projects sp ON grh.project_id = sp.id
            """))

            db.execute(text("DROP TABLE gpt_rank_history"))
            db.execute(text("ALTER TABLE gpt_rank_history_new RENAME TO gpt_rank_history"))
            print("✅ Updated GPTRankHistory table structure")

        # Check KeywordResearch
        research_count = db.execute(text("SELECT COUNT(*) FROM keyword_research")).scalar()
        print(f"Found {research_count} research records")

        if research_count > 0:
            print("📝 Updating KeywordResearch table...")
            db.execute(text("""
                CREATE TABLE keyword_research_new AS
                SELECT
                    kr.id, kr.research_id, sp.project_id, kr.topic, kr.target_language, kr.research_type,
                    kr._intent_analysis, kr._keyword_data, kr._competitor_analysis, kr._content_gaps,
                    kr.created_at, kr.updated_at
                FROM keyword_research kr
                JOIN seo_gpt_projects sp ON kr.project_id = sp.id
            """))

            db.execute(text("DROP TABLE keyword_research"))
            db.execute(text("ALTER TABLE keyword_research_new RENAME TO keyword_research"))
            print("✅ Updated KeywordResearch table structure")
        
        print("🎉 Database migration completed successfully!")
        print("✅ All foreign key relationships now use string UUIDs")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        db.rollback()
        raise e
    finally:
        db.close()

if __name__ == "__main__":
    run_migration()
