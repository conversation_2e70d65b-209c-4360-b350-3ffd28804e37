#!/usr/bin/env python3
"""
Test script to verify JWT authentication with a real token from the frontend.
Run this after logging into the frontend and getting a real JWT token.
"""

import asyncio
import logging
import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.auth import verify_jwt_token
from app.core.supabase import SUPABASE_URL, SUPABASE_ANON_KEY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Backend URL
BACKEND_URL = "http://localhost:8000"

async def test_jwks_direct():
    """Test JWKS endpoint directly with different approaches."""
    print("\n🔍 Testing JWKS Endpoint Directly...")
    print("=" * 50)
    
    jwks_url = f"{SUPABASE_URL}/auth/v1/jwks"
    print(f"JWKS URL: {jwks_url}")
    
    # Test 1: With anon key
    print("\n1. Testing with anon key...")
    try:
        headers = {"apikey": SUPABASE_ANON_KEY}
        response = requests.get(jwks_url, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            jwks = response.json()
            print(f"   ✅ Success! Found {len(jwks.get('keys', []))} keys")
            return jwks
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Without API key
    print("\n2. Testing without API key...")
    try:
        response = requests.get(jwks_url, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            jwks = response.json()
            print(f"   ✅ Success! Found {len(jwks.get('keys', []))} keys")
            return jwks
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Alternative URL format
    print("\n3. Testing alternative URL format...")
    try:
        alt_url = f"{SUPABASE_URL}/.well-known/jwks.json"
        response = requests.get(alt_url, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            jwks = response.json()
            print(f"   ✅ Success! Found {len(jwks.get('keys', []))} keys")
            return jwks
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return None

async def test_with_real_token():
    """Test authentication with a real JWT token."""
    print("\n🔍 Testing with Real JWT Token...")
    print("=" * 50)
    
    # Instructions for getting token
    print("To get a real JWT token:")
    print("1. Go to http://localhost:3002 and log in")
    print("2. Open Developer Tools (F12)")
    print("3. Go to Application → Local Storage")
    print("4. Look for Supabase auth data")
    print("5. Copy the access_token value")
    print("6. Paste it below when prompted")
    print()
    
    # Get token from user input
    token = input("Paste your JWT token here (or press Enter to skip): ").strip()
    
    if not token:
        print("⏭️  Skipping real token test")
        return False
    
    print(f"\n🔍 Testing token (length: {len(token)} chars)...")
    
    # Test 1: Direct JWT verification
    print("\n1. Testing direct JWT verification...")
    try:
        payload = await verify_jwt_token(token)
        print("   ✅ JWT verification successful!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Issued at: {payload.get('iat')}")
        print(f"   Expires at: {payload.get('exp')}")
        
        # Test 2: Backend endpoint with real token
        print("\n2. Testing backend endpoint with real token...")
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Got response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"   ❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ JWT verification failed: {e}")
        return False

async def test_supabase_config():
    """Test Supabase configuration."""
    print("\n🔍 Testing Supabase Configuration...")
    print("=" * 50)
    
    print(f"Supabase URL: {SUPABASE_URL}")
    print(f"Anon Key: {SUPABASE_ANON_KEY[:20]}...")
    
    # Test basic Supabase connectivity
    try:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", timeout=10)
        print(f"REST API Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Supabase REST API accessible")
        else:
            print(f"❌ Supabase REST API error: {response.text}")
            
    except Exception as e:
        print(f"❌ Supabase connectivity error: {e}")

async def main():
    """Run all tests."""
    print("🧪 Real JWT Token Authentication Test")
    print("=" * 60)
    
    # Test Supabase configuration
    await test_supabase_config()
    
    # Test JWKS endpoint
    jwks = await test_jwks_direct()
    
    # Test with real token
    token_success = await test_with_real_token()
    
    print("\n📊 Summary")
    print("=" * 30)
    print(f"JWKS Endpoint: {'✅ Working' if jwks else '❌ Failed'}")
    print(f"Real Token Test: {'✅ Working' if token_success else '❌ Failed or Skipped'}")
    
    if jwks and token_success:
        print("\n🎉 Authentication is working correctly!")
    elif jwks:
        print("\n🔧 JWKS is working, but need to test with real token")
    else:
        print("\n🔧 JWKS endpoint issue needs to be resolved")

if __name__ == "__main__":
    asyncio.run(main())
