#!/usr/bin/env python3
"""
Test JWT authentication with a fallback approach that doesn't require JWKS validation.
This will help us test if the JWT tokens are valid even if <PERSON><PERSON><PERSON> endpoint is not accessible.
"""

import asyncio
import logging
import sys
import os
import requests
import json
import jwt
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.supabase import SUPABASE_URL, SUPABASE_ANON_KEY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Backend URL
BACKEND_URL = "http://localhost:8000"

async def verify_jwt_token_fallback(token: str) -> Dict[str, Any]:
    """
    Verify JWT token without signature verification (for testing only).
    This is NOT secure for production but helps us test the token structure.
    """
    try:
        # Decode without verification (TESTING ONLY)
        payload = jwt.decode(token, options={"verify_signature": False})
        
        # Check basic structure
        required_fields = ["sub", "email", "iat", "exp"]
        for field in required_fields:
            if field not in payload:
                raise ValueError(f"Missing required field: {field}")
        
        # Check if token is expired
        import time
        current_time = int(time.time())
        if payload.get("exp", 0) < current_time:
            raise ValueError("Token has expired")
        
        # Check issuer
        expected_issuer = f"{SUPABASE_URL}/auth/v1"
        if payload.get("iss") != expected_issuer:
            print(f"⚠️  Warning: Issuer mismatch. Expected: {expected_issuer}, Got: {payload.get('iss')}")
        
        return payload
        
    except Exception as e:
        raise ValueError(f"Token validation failed: {e}")

async def test_supabase_rest_api():
    """Test if we can access Supabase REST API with proper headers."""
    print("\n🔍 Testing Supabase REST API Access...")
    print("=" * 50)
    
    try:
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
        }
        
        # Test basic REST API endpoint
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", headers=headers, timeout=10)
        print(f"REST API Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Supabase REST API accessible")
            return True
        else:
            print(f"❌ REST API Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ REST API Exception: {e}")
        return False

async def test_auth_endpoint():
    """Test if we can access Supabase Auth endpoints."""
    print("\n🔍 Testing Supabase Auth Endpoints...")
    print("=" * 50)
    
    endpoints_to_test = [
        "/auth/v1/settings",
        "/auth/v1/health",
        "/auth/v1/jwks",
        "/.well-known/jwks.json"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            url = f"{SUPABASE_URL}{endpoint}"
            print(f"\nTesting: {endpoint}")
            
            # Try with anon key
            headers = {"apikey": SUPABASE_ANON_KEY}
            response = requests.get(url, headers=headers, timeout=5)
            print(f"   With API key: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Success: {endpoint}")
                if "jwks" in endpoint:
                    try:
                        jwks = response.json()
                        print(f"   Found {len(jwks.get('keys', []))} keys")
                        return jwks
                    except:
                        pass
            
            # Try without API key
            response_no_key = requests.get(url, timeout=5)
            print(f"   Without API key: {response_no_key.status_code}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

async def test_with_sample_token():
    """Test with a sample JWT token structure."""
    print("\n🔍 Testing with Sample Token...")
    print("=" * 50)
    
    print("Please provide a JWT token from your frontend:")
    print("1. Go to http://localhost:3002")
    print("2. Log in with your credentials")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Application → Local Storage → supabase.auth.token")
    print("5. Copy the access_token value")
    print()
    
    token = input("Paste your JWT token here: ").strip()
    
    if not token:
        print("⏭️  No token provided, skipping test")
        return False
    
    print(f"\n🔍 Analyzing token (length: {len(token)} chars)...")
    
    try:
        # Test 1: Decode without verification
        print("\n1. Decoding token structure...")
        payload = await verify_jwt_token_fallback(token)
        
        print("   ✅ Token structure is valid!")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Issuer: {payload.get('iss')}")
        print(f"   Audience: {payload.get('aud')}")
        print(f"   Issued at: {payload.get('iat')}")
        print(f"   Expires at: {payload.get('exp')}")
        
        # Test 2: Test with backend endpoint
        print("\n2. Testing with backend endpoint...")
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BACKEND_URL}/api/moodboard/list", headers=headers, timeout=10)
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Backend accepted token! Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"   ❌ Backend rejected token: {response.text}")
            
            # Check if it's a JWKS issue
            if "Unable to find appropriate key" in response.text or "fetch JWKS" in response.text:
                print("   🔍 This appears to be a JWKS endpoint issue, not a token issue")
                print("   The token structure is valid but JWKS verification is failing")
            
            return False
            
    except Exception as e:
        print(f"   ❌ Token analysis failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 JWT Authentication Fallback Test")
    print("=" * 60)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Supabase URL: {SUPABASE_URL}")
    print()
    
    # Test Supabase connectivity
    rest_api_ok = await test_supabase_rest_api()
    
    # Test auth endpoints
    jwks = await test_auth_endpoint()
    
    # Test with real token
    token_ok = await test_with_sample_token()
    
    print("\n📊 Test Summary")
    print("=" * 40)
    print(f"Supabase REST API: {'✅ Working' if rest_api_ok else '❌ Failed'}")
    print(f"JWKS Endpoint: {'✅ Working' if jwks else '❌ Failed'}")
    print(f"Token Test: {'✅ Working' if token_ok else '❌ Failed'}")
    
    print("\n🔧 Recommendations:")
    if not jwks:
        print("❌ JWKS endpoint is not accessible")
        print("   - This might be a Supabase configuration issue")
        print("   - Check if your Supabase project is properly configured")
        print("   - Verify the Supabase URL is correct")
    
    if token_ok:
        print("✅ JWT tokens are working correctly")
        print("   - The authentication logic is sound")
        print("   - Issue is likely in JWKS endpoint configuration")
    elif jwks:
        print("✅ JWKS endpoint is working")
        print("   - Issue might be in token generation or format")

if __name__ == "__main__":
    asyncio.run(main())
