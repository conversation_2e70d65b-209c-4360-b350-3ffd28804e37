/**
 * Tipos compartidos para el sistema de agentes
 */

/**
 * Tipos de contenido que pueden generarse
 */
export enum ContentType {
  INSTAGRAM_POST = "INSTAGRAM_POST",
  FACEBOOK_POST = "FACEBOOK_POST",
  TWITTER_POST = "TWITTER_POST",
  BLOG_POST = "BLOG_POST",
  EMAIL_CAMPAIGN = "EMAIL_CAMPAIGN",
  LANDING_PAGE = "LANDING_PAGE",
  PRODUCT_DESCRIPTION = "PRODUCT_DESCRIPTION",
  AD_COPY = "AD_COPY",
  PRESS_RELEASE = "PRESS_RELEASE",
  SOCIAL_MEDIA_BIO = "SOCIAL_MEDIA_BIO",
  VIDEO_SCRIPT = "VIDEO_SCRIPT"
}

/**
 * Tipos de mensajes en la conversación
 */
export enum MessageType {
  SYSTEM = "SYSTEM",
  THINKING = "THINKING",
  MESSAGE = "MESSAGE",
  ERROR = "ERROR",
  TIMEOUT = "TIMEOUT", // Nuevo tipo para timeouts
  USER = "USER"
}

/**
 * Estado de generación de contenido
 */
export enum GenerationStatus {
  IDLE = "idle",
  INITIALIZING = "initializing",
  IN_PROGRESS = "in_progress",
  PENDING = "PENDING",
  GENERATING = "GENERATING",
  COMPLETED = "COMPLETED",
  FAILED = "failed",
  ERROR = "ERROR"
}

/**
 * Datos de un agente
 */
export interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  systemPrompt: string;
  temperature?: number;
  color: string;
  emoji?: string;
  order?: number;
  // Propiedades adicionales para agentManager.ts
  strengths?: string[];
  expertise?: string[];
  avatar?: string;
}

/**
 * Mensaje de un agente
 */
export interface AgentMessage {
  id: string;
  timestamp: number;
  agentId: string;
  type: MessageType;
  content: string;
}

/**
 * Solicitud para iniciar una conversación
 */
export interface StartConversationRequest {
  contentType: ContentType;
  targetAudience: string;
  productInfo: string;
  toneVoice: string;
  additionalContext?: string | null;
}

/**
 * Respuesta de inicio de conversación
 */
export interface StartConversationResponse {
  sessionId: string;
  agents: Agent[];
  initialMessage: AgentMessage;
}

/**
 * Solicitud para obtener mensajes
 */
export interface GetMessagesRequest {
  sessionId: string;
  since?: number;
}

/**
 * Respuesta con mensajes
 */
export interface GetMessagesResponse {
  messages: AgentMessage[];
  status: GenerationStatus;
  agents?: Agent[]; // Incluir los agentes en la respuesta
}

/**
 * Respuesta con contenido generado
 */
export interface GetContentResponse {
  content: string;
  status: GenerationStatus;
}

/**
 * Respuesta de estado de generación
 */
export interface GetStatusResponse {
  status: GenerationStatus;
}

/**
 * Resultado de una generación
 */
export interface GenerationResult {
  content: string;
  status: GenerationStatus;
}

/**
 * Solicitud para enviar un mensaje a un agente
 */
export interface SendMessageRequest {
  sessionId: string;
  agentId: string;
  message: string;
}

/**
 * Respuesta al enviar un mensaje a un agente
 */
export interface SendMessageResponse {
  message: AgentMessage;
}

// Additional types for UI components
export interface UIAgent extends Agent {
  isSelected?: boolean;
  lastActivity?: Date;
  performance?: {
    successRate: number;
    averageResponseTime: number;
  };
}

export interface UIAgentMessage extends AgentMessage {
  isLoading?: boolean;
  error?: string;
  reactions?: string[];
}

export interface Interaction {
  id: string;
  agentId: string;
  userId: string;
  type: 'message' | 'task' | 'feedback';
  content: string;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  result?: any;
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  agentId?: string;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

export interface UIWorkflowState {
  currentStep: number;
  totalSteps: number;
  steps: WorkflowStep[];
  isRunning: boolean;
  error?: string;
  userQuery?: string;
}

// Image and Content Types
export interface GeneratedContentImage {
  id: string;
  url: string;
  prompt: string;
  style?: string;
  dimensions: {
    width: number;
    height: number;
  };
  metadata?: Record<string, any>;
}

export interface UploadedImage {
  id: string;
  url: string;
  filename: string;
  size: number;
  type: string;
  width: number;
  height: number;
  uploadedAt: Date;
}